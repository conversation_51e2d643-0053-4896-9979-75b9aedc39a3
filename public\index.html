<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tutors Alliance Scotland - Landing</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/responsive-helper.js"></script>
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>
    <!-- JSON-LD microdata for Tutors Alliance Scotland as an EducationalOrganization -->
    <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "EducationalOrganization",
          "name": "Tutors Alliance Scotland",
          "url": "https://tutorsalliancescotland.co.uk",
          "foundingDate": "2025-03-27",
          "founder": {
            "@type": "Person",
            "name": "<PERSON>"
          },
          "logo": "https://tutorsalliancescotland.co.uk/images/centralShield.png",
          "description": "Raising Standards, Shaping Futures in Scottish education. Providing resources, training, and recognition for tutors.",
          "sameAs": [
            "https://www.facebook.com/TutorsAllianceScotland",
            "https://twitter.com/TutorsAllianceScotland"
          ]
        }
    </script>
</head>
<body data-page="index">
    <header>
        <h1>Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="login.html?role=admin" class="banner-login-link login-box">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS will populate tutor names/subjects here -->
        </div>
    </div>
    <main>
        <div class="mission-row">
            <!-- RIGHT COLUMN: Shield + Ribbons -->
            <div class="left-col">
                <img src="/images/centralShield.png" alt="Large TAS Shield" class="main-shield" id="imageShield">
                <img src="/images/bannerWithRibbons.png" alt="Banner Ribbon" class="main-ribbons" id="imageBanner">
            </div>

            <!-- LEFT COLUMN: heading + about-us text -->
            <div class="right-col">
                <div class="about-us-landing" id="aboutUsLanding">
                    <h1 class="mission-statement">Welcome to Tutors Alliance Scotland</h1>
                    <a class="button aurora" href="tutorMembership.html">Join as a Tutor</a>
                </div>
            </div>
        </div>

        <!-- HERO: Full-width background image with overlay text -->
        <section class="hero-banner fade-in-section">
            <div class="hero-content">
                <h1>Tutors Alliance Scotland</h1>
                <p>Raising Standards, Shaping Futures. Scotland's professional membership organisation for tutors.</p>
                <a class="button aurora" href="about-us.html">About us</a>
            </div>
        </section>

        <!-- ABOUT: Two-column info block -->
        <section class="two-col-content fade-in-section">
            <div>
                <h2>Raising Standards and Shaping Futures Through Tuition</h2>
                <p>
                    Tutors Alliance Scotland is a not-for-profit professional membership organisation created by experienced educators for fully qualified teachers who tutor in Scotland. We are committed to setting high standards and to raise the profile of tutoring across Scotland.<br><br>
                    We are here to set a standard, support tutors, guide parents and ensure every pupil has access to safe, high-quality tuition, no matter their background or postcode.
                </p>
                <p>
                    We believe that tutoring is a powerful tool for bridging the attainment gap in Scotland. By providing tutors with resources, training and professional recognition, we ensure that families can trust and access the best possible support for their children.
                </p>
                <a href="contact.html" class="button aurora">Contact Us</a>
            </div>
            <div>
                <img src="/images/karenAndMartine.PNG" alt="Tutoring in action" />
            </div>
        </section>

        <!-- PARENT ZONE SECTION -->
        <section class="parents-zone-section fade-in-section">
            <div class="zone-gradient-bg parent-gradient-bg">
                <div class="zone-list-row">
                    <div class="parents-box">
                        <h2>👨‍👩‍👧‍👦 For Parents – Helping You Choose the Right Tutor With Confidence</h2>
                        <div class="questionmark-row">
                            <div class="questionmark-stack parent-stack-left">
                                <img src="/images/questionCurl.png" alt="Question Mark Top" class="parent-img-list parent-img-list-left">
                                <img src="/images/questionPeriod.png" alt="Question Mark Bottom" class="parent-img-list parent-img-list-left-period">
                            </div>
                            <div class="questionmark-stack parent-stack-right">
                                <img src="/images/questionCurl.png" alt="Question Mark Top" class="parent-img-list parent-img-list-right">
                                <img src="/images/questionPeriod.png" alt="Question Mark Bottom" class="parent-img-list parent-img-list-right-period">
                            </div>
                        </div>
                        <p>
                            We understand how overwhelming it can be to find a tutor you can trust.<br>
                            That's why we're creating a national standard so parents know what to look for and who to trust.<br>
                            Here's how we help:
                        </p>
                        <ul class="parent-list" style="text-align:left; max-width: 500px; margin: 1rem auto;">
                            <li>✅ Signposting trusted, vetted tutors in your area or online</li>
                            <li>📖 Clear guidance on what to expect from a high-quality tutor</li>
                            <li>🧠 Advice around learning needs, gaps and progress</li>
                            <li>💬 Support with questions around learning and school challenges</li>
                            <li>💡 Access to tips, tools and webinars for supporting your child at home</li>
                        </ul>
                        <div class="button-group">
                            <a class="button aurora" href="parents.html">Enter Parent Zone</a>
                            <a class="button aurora" href="tutorDirectory.html">Tutor Directory</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- TUTOR ZONE SECTION -->
        <section class="tutor-zone-section fade-in-section">
            <div class="zone-gradient-bg tutor-gradient-bg">
                <div class="zone-list-row">
                    <img src="/images/legoLeft.png" alt="Lego Left" class="tutor-img-list tutor-img-list-left">
                    <div class="tutor-box">
                        <h2>👩‍🏫 For Tutors – A Professional Home for Tutors in Scotland</h2>
                        <p>
                            Join a growing network of tutors who are committed to high standards and continuous improvement.
                            We support teachers tutoring in Scotland to develop their skills, grow their businesses and access opportunities that make a real difference.
                        </p>
                        <p>As a member, you'll gain access to:</p>
                        <ul class="tutor-list" style="text-align:left; max-width: 500px; margin: 1rem auto;">
                            <li>✅ Professional recognition through membership</li>
                            <li>📚 Ongoing CPD, training and accreditation</li>
                            <li>🤝 A supportive community of like-minded tutors</li>
                            <li>🎯 Opportunities to deliver funded or partnered tuition</li>
                            <li>🔐 Guidance on safeguarding, standards and best practice</li>
                            <li>🧾 Resources to help with contracts, policies and business growth</li>
                        </ul>
                        <div class="button-group">
                            <a class="button aurora" href="tutorszone.html">Enter Tutor Zone</a>
                            <a class="button aurora" href="tutorMembership.html">Become a Member</a>
                        </div>
                    </div>
                    <img src="/images/legoRight.png" alt="Lego Right" class="tutor-img-list tutor-img-list-right">
                </div>
            </div>
        </section>

        <!-- PUPIL SECTION -->
        <section class="pupil-zone-section fade-in-section">
            <div class="zone-gradient-bg pupil-gradient-bg">
                <div class="zone-list-row">
                    <img src="/images/weAreTheFuture.PNG" alt="We Are The Future Left" class="pupil-img-list pupil-img-list-left">
                    <div class="pupil-box">
                        <h2>🎒 For Pupils – Because Every Learner Deserves to Thrive</h2>
                        <p>Whether it's about catching up, getting ahead or gaining confidence, the right tuition can transform a child's learning journey.</p>
                        <p>We're working to make this a reality by:</p>
                        <ul class="pupil-list" style="text-align:left; max-width: 500px; margin: 1rem auto;">
                            <li>🎓 Connecting pupils with trusted, trained tutors</li>
                            <li>💬 Creating safe, positive learning environments</li>
                            <li>🧩 Supporting pupils with additional support needs (ASN)</li>
                            <li>🎁 Offering funded tuition to pupils facing disadvantage</li>
                            <li>🧭 Providing goal-led tuition that builds confidence and closes gaps</li>
                        </ul>
                        <div class="button-group">
                            <a class="button aurora" href="/blog">How Tutoring Helps</a>
                        </div>
                    </div>
                    <img src="/images/weAreTheFuture.PNG" alt="We Are The Future Right" class="pupil-img-list pupil-img-list-right">
                </div>
            </div>
        </section>

        <!-- STRIVE VALUES SECTION -->
        <section class="strive-bg-section fade-in-section">
            <div class="strive-overlay-card curve-bottom-left strive-small-card">
                <h2>🤝 We STRIVE for Better Tuition in Scotland</h2>
                <div class="strive-values">
                    <div class="strive-value"><div class="strive-icon">S</div><p><strong>Standards</strong> – setting a benchmark for professional tutoring</p></div>
                    <div class="strive-value"><div class="strive-icon">T</div><p><strong>Trust</strong> – safeguarding, transparency and accountability</p></div>
                    <div class="strive-value"><div class="strive-icon">R</div><p><strong>Recognition</strong> – giving tutors the respect and status they deserve</p></div>
                    <div class="strive-value"><div class="strive-icon">I</div><p><strong>Impact</strong> – ensuring all tuition leads to meaningful outcomes</p></div>
                    <div class="strive-value"><div class="strive-icon">V</div><p><strong>Voice</strong> – listening to tutors, parents and pupils</p></div>
                    <div class="strive-value"><div class="strive-icon">E</div><p><strong>Equity</strong> – reducing barriers to access for every learner</p></div>
                </div>
                <div class="button-group">
                    <a class="button aurora" href="about-us.html">What is our mission</a>
                </div>
            </div>
        </section>

        <!-- CTA BUTTONS WITH BACKGROUND -->
        <section class="cta-banner fade-in-section">
            <div class="cta-content">
                <div class="cta-buttons">
                    <a class="button aurora" href="tutorDirectory.html">Find a Tutor</a>
                    <a class="button aurora" href="contact.html">Contact Us</a>
                    <a class="button aurora" href="tutorMembership.html">Become a Member</a>
                </div>
            </div>
        </section>

        <!-- CONFIDENCE BLOCK: Two-column info -->
        <section class="two-col-content confidence-block">
            <div>
                <h2>Confidence for Parents</h2>
                <p>All our tutors are fully qualified teachers, PVG-checked, and committed to Scotland's highest educational standards.</p>
            </div>
            <div>
                <img src="/images/centralShield.png" alt="TAS Shield" class="confidence-img-right" />
            </div>
        </section>

        <!-- TESTIMONIALS: Card placeholders -->
        <section class="testimonials-bg-section fade-in-section testimonials-laced">
            <div class="testimonial-quote-card" style="top:10%;left:12%;"><p>"TAS helped me connect with families who value high standards."<br><span>- Tutor, Edinburgh</span></p></div>
            <div class="testimonial-quote-card" style="top:40%;right:10%;"><p>"I trust TAS tutors because they're all qualified teachers."<br><span>- Parent, Glasgow</span></p></div>
            <div class="testimonial-quote-card" style="bottom:12%;left:30%;"><p>"Great community and support for tutors."<br><span>- Tutor, Aberdeen</span></p></div>
        </section>

        <!-- NEWSLETTER SECTION -->
        <section class="newsletter-zone-section fade-in-section">
            <div class="zone-gradient-bg newsletter-gradient-bg">
                <div class="newsletter-box">
                    <h2>📩 Stay in the Loop with the TAS Newsletter!</h2>
                    <p>Want to be at the heart of the movement to improve tuition in Scotland? Our TAS Newsletter keeps you updated on:</p>
                    <ul style="text-align:left; max-width: 600px; margin: 1rem auto;">
                        <li>📢 Latest developments in tuition and education policy</li>
                        <li>🤝 Opportunities to contribute to shaping the future of Scottish tutoring</li>
                        <li>📅 Upcoming events and training for tutors and educators</li>
                    </ul>
                    <p>But this isn't just any newsletter, it's interactive! 🎯 We're launching our Scottish Tutoring Action Plan, a dynamic space where you can share your expertise, insights and ideas to help shape the future of tuition. This is your chance to add real value to the sector and make an impact!</p>
                    <div class="newsletter-cta">
                        <p>📬 Sign up now and be part of the change:</p>
                        <a href="https://subscribepage.io/VutNA7" class="subscribe-link newsletter-blue-hover">
                            <img src="/images/subscribe-computer.PNG" alt="Subscribe to our newsletter" class="subscribe-image">
                        </a>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Clear separator before dynamic sections -->
    <div class="dynamic-sections-separator"></div>

    <!-- Dynamic sections will be added here -->
    <section id="dynamicSections"></section>

    <!-- SOCIAL ICONS FOOTER -->
    <footer class="site-footer fade-in-section">
        <div class="footer-icons">
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
        </div>
    </footer>

    <!-- STATIC BOTTOM FOOTER -->
    <footer class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4>Extra Information</h4>
                <ul>
                    <li><a href="tutoring-standards.html">The TAS Way: Governance and Guidance</a></li>
                    <li><a href="faq.html">FAQ's</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="safeguarding-policy.html">Safeguarding Policy</a></li>
                    <li><a href="terms-and-conditions.html">Terms and Conditions</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p>ALL RIGHTS RESERVED © Tutors Alliance Scotland 2025</p>
                </div>
                <div class="static-footer-credits">
                    <p>Website by <a href="#" target="_blank">Tutors Alliance Scotland</a></p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="website-url">
                    <p>www.tutorsalliancescotland.org.uk</p>
                </div>
            </div>
        </div>
    </footer>


    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <script>
        function goToLogin(role) {
            window.location.href = `login.html?role=${encodeURIComponent(role)}`;
        }

        // Rolling banner fetch - try news first, fallback to tutors
        fetch('/api/sections?page=rolling-banner')
            .then(res => res.json())
            .then(sections => {
                if (sections && sections.length > 0) {
                    // Format news information - join all text content with separator
                    const text = sections.map(s => s.text).join(' | ');
                    document.getElementById('tutorBanner').innerText = text;
                } else {
                    // Fallback to tutors if no news sections are found
                    return fetch('/api/tutors?format=json')
                        .then(res => res.json())
                        .then(tutors => {
                            const text = tutors.map(t => `${t.name} (${t.subjects.join(', ')})`).join(' | ');
                            document.getElementById('tutorBanner').innerText = text;
                        });
                }
            })
            .catch(err => console.error('Error fetching banner content:', err));


        window.addEventListener('DOMContentLoaded', () => {
            // After the heading's animation delay (say 1.5s) plus a little buffer:
            setTimeout(() => {
                document.querySelectorAll('.fade-later').forEach(el => {
                    el.classList.add('fade-in');
                });
            }, 1500); // or 2000 if you want a bit more buffer
        });

        // All fade-in logic is now handled centrally by responsive-helper.js



    </script>
    <!-- Fade-in animations are now handled centrally by responsive-helper.js -->
    <script type="module">
        const page = location.pathname.replace(/^\//, '').split('.')[0] || 'index';

        fetch(`/api/sections?page=${page}`)
            .then(r => r.json())
            .then(list => {
                if (list && list.length > 0) {
                    // Only show the dynamic sections container if there are sections to display
                    const host = document.getElementById('dynamicSections');

                    // Clear any existing content
                    host.innerHTML = '';

                    // Add each section with proper spacing and styling
                    list.forEach((s, index) => {
                        host.insertAdjacentHTML('beforeend', `
                              <article class="dyn-block fade-in-section" style="transition-delay: ${index * 0.1}s">
                                ${s.image ? `<img src="${s.image}" alt="${s.heading}" loading="lazy">` : ''}
                                <h2>${s.heading}</h2>
                                <div class="dyn-content">${s.text}</div>
                              </article>`);
                    });

                    // Make sure the dynamic sections container is visible
                    host.style.display = 'block';

                    // Ensure the separator is visible
                    document.querySelector('.dynamic-sections-separator').style.display = 'block';

                    // Force layout recalculation to ensure proper positioning
                    document.body.offsetHeight;

                    // Dynamic blocks will be automatically observed by responsive-helper.js
                    
                    // Scroll to the dynamic sections if they were added via admin
                    const urlParams = new URLSearchParams(window.location.search);
                    if (urlParams.get('scrollToNew') === 'true') {
                        setTimeout(() => {
                            host.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }, 500);
                    }
                } else {
                    // If no dynamic sections, hide the container and separator
                    const host = document.getElementById('dynamicSections');
                    host.style.display = 'none';
                    document.querySelector('.dynamic-sections-separator').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading dynamic sections:', error);
                // Hide the container and separator on error
                document.getElementById('dynamicSections').style.display = 'none';
                document.querySelector('.dynamic-sections-separator').style.display = 'none';
            });
    </script>

    <!-- Single Intersection Observer for all fade-in elements -->
    <!-- Load dynamic sections from the shared module -->
    <script src="/js/rolling-banner.js" defer></script>   <!-- *uses* initRollingBanner -->
    <script src="/js/visual-editor.js" defer></script>

    <!-- bump the version whenever you redeploy -->
    <script src="/js/dynamic-sections.js?v=20240530" type="module" defer></script>
</body>
</html>