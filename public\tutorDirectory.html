<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tutors Alliance Scotland - Helping all Scottish children grow</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <script src="/responsive-helper.js"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>
</head>
<body class="tutor-directory-page restricted-viewport" style="background-color: #F0F8FF;" data-page="tutorDirectory">
    <!-- Shared banner/header -->
    <header>
        <h1>Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="index.html" class="banner-login-link login-box">Home</a>
            <a href="login.html?role=admin" class="banner-login-link login-box">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->


    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS will populate tutor names/subjects here -->
        </div>
    </div>


    <main>
        <div class="content-flex-wrapper">
            <!-- LEFT COLUMN: Shield + Ribbons -->
            <div class="left-col">
                <img src="/images/centralShield.png" alt="Large STA Shield" class="main-shield" id="imageShield">
                <img src="/images/bannerWithRibbons.png" alt="Banner Ribbon" class="main-ribbons" id="imageBanner">
            </div>

            <!-- RIGHT COLUMN: heading + about-us text -->
            <div class="right-col">

                <!-- TOP dynamic sections container -->
                <section id="dynamicSectionsTop" class="dynamic-section-container"></section>


                <div class="about-us-landing" id="aboutUsLanding">
                    <h1 class="mission-statement" style="color: #0057B7;">Find a Tutor</h1>
                    <p>
                        Use this search form to find tutors by subject and location. All tutors are fully
                        qualified teachers, covered by statutory safeguarding guidelines.
                    </p>
                </div>

                <style>
                    /* Center heading in portrait orientation on restricted viewport devices */
                    @media (max-width: 900px) and (orientation: portrait) {
                        .mission-statement {
                            text-align: center !important;
                            margin-left: auto !important;
                            margin-right: auto !important;
                        }

                        .about-us-landing p {
                            text-align: center !important;
                        }
                    }
                </style>

                <div class="form-container" style="width: 80%; max-width: 500px; background-color: #fff; border-radius: 1rem; padding: 2rem; box-shadow: 0 2px 18px rgba(0,87,183,0.05); margin: 2rem auto;">
                    <h2 style="color: #001B44; margin-bottom: 1.5rem; text-align: center;">Tutor Finder</h2>
                    <form id="tutorFinderForm" action="/tutors" method="GET" style="display: flex; flex-direction: column; gap: 1.5rem; align-items: center;">
                        <div style="width: 100%; text-align: center;">
                            <label for="subject" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333; text-align: center;">Subject</label>
                            <select name="subject" id="subject" style="width: 80%; max-width: 300px; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                                <option value="mathematics">Mathematics</option>
                                <option value="english">English</option>
                            </select>
                        </div>

                        <div style="width: 100%; text-align: center;">
                            <label for="mode" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333; text-align: center;">Location</label>
                            <select name="mode" id="mode" style="width: 80%; max-width: 300px; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                                <option value="online">Online</option>
                                <option value="in-person">In Person</option>
                            </select>
                        </div>

                        <div id="postcodeContainer" style="display: none; width: 100%; text-align: center;">
                            <label for="postcode" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333; text-align: center;">Postcode</label>
                            <input type="text" name="postcode" id="postcode" placeholder="Enter your postcode" style="width: 80%; max-width: 300px; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                        </div>

                        <button type="submit" class="button aurora" style="width: 80%; max-width: 300px; background-color: #0057B7; color: white; padding: 1rem; border: none; border-radius: 0.5rem; font-size: 1.1rem; font-weight: bold; cursor: pointer; margin-top: 0.5rem;">Find Tutors</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- MIDDLE dynamic sections container -->
        <section id="dynamicSectionsMiddle" class="dynamic-section-container"></section>

        <!-- Clear separator before dynamic sections -->
        <div class="dynamic-sections-separator"></div>

        <!-- BOTTOM dynamic sections container -->
        <section id="dynamicSections" class="dynamic-section-container"></section>
    </main>


    <script>
        function goToLogin(role) {
            window.location.href = `login.html?role=${encodeURIComponent(role)}`;
        }
    </script>

    // Initialize the rolling banner using responsive-helper.js
    <script src="/js/visual-editor.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            initRollingBanner();
        });


        window.addEventListener('DOMContentLoaded', () => {
            // After the heading's animation delay (say 1.5s) plus a little buffer:
            setTimeout(() => {
                document.querySelectorAll('.fade-later').forEach(el => {
                    el.classList.add('fade-in');
                });
            }, 1500); // or 2000 if you want a bit more buffer
        });




        document.getElementById('mode').addEventListener('change', function () {
            const postcodeContainer = document.getElementById('postcodeContainer');
            if (this.value === 'in-person') {
                postcodeContainer.style.display = 'block';
            } else {
                postcodeContainer.style.display = 'none';
            }
        });

        document.getElementById('tutorFinderForm').addEventListener('submit', function (event) {
            event.preventDefault();

            const subject = document.getElementById('subject').value;
            const mode = document.getElementById('mode').value;
            let postcode = document.getElementById('postcode').value;

            if (postcode) {
                postcode = postcode.toUpperCase();
            }

            let queryParams = `?subject=${encodeURIComponent(subject)}&mode=${encodeURIComponent(mode)}`;
            if (mode === "in-person" && postcode.trim() !== "") {
                queryParams += `&postcode=${encodeURIComponent(postcode)}`;
            }

            window.location.href = `/tutors/search${queryParams}`;
        });
    </script>
    <script>
        // Fade-in animation for sections
        const fadeEls = document.querySelectorAll('.fade-in-on-scroll');

        // Set initial styles
        fadeEls.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
        });

        // Create the Intersection Observer
        const observer = new IntersectionObserver((entries, obs) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Fade it in
                    entry.target.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';

                    // Once triggered, stop observing so it doesn't re-animate if user scrolls away
                    obs.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 }); // threshold=0.1 => trigger at 10% visibility

        // Observe each fadeEl
        fadeEls.forEach(el => observer.observe(el));
    </script>

    <!-- Load the dynamic sections script -->
    <script src="/js/dynamic-sections.js?v=20240530" type="module" defer></script>



</body>
</html>

