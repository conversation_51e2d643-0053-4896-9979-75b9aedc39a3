/* ===================================================================== */
/* MAIN NAVIGATION STYLES - Dropdown & Mobile Responsive                */
/* ===================================================================== */

/* === GENERAL LAYOUT ================================================== */
.main-nav {
    background: #001B44;
    position: relative;
    z-index: 1000;
    width: 100%;
}

.main-nav .menu {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav .menu-item {
    position: relative;
    border-left: 1px solid #ADD8E6;
}

.main-nav .menu-item:first-child {
    border-left: none;
}

.main-nav a {
    display: block;
    padding: 14px 20px;
    color: #fff;
    text-decoration: none;
    font-size: 1.1em;
    white-space: nowrap;
    transition: background-color 0.3s ease;
}

.main-nav a:hover,
.main-nav .menu-item:hover > a,
.main-nav .menu-item:focus-within > a {
    background: #003F8F;
}

/* === DROPDOWN SUBMENUS =============================================== */
.has-submenu {
    position: relative;
}

/* Dropdown arrow indicator */
.has-submenu > a::after {
    content: ' ▾';
    font-size: 0.8em;
    margin-left: 0.5em;
}

/* 🔑 CRITICAL: Hide ALL nested ul elements by default */
.main-nav ul ul {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: #003F8F;
    min-width: 240px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 0 0 8px 8px;
    border-top: 3px solid #00a9ce;
    z-index: 1001;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-direction: column;
}

/* 🔑 CRITICAL: Show submenus on hover/focus (desktop) or when .open class is applied (mobile) */
.has-submenu:hover > ul,
.has-submenu:focus-within > ul,
.has-submenu.open > ul {
    display: flex;
}

.main-nav ul ul li {
    border-left: none;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    width: 100%;
}

.main-nav ul ul li:last-child {
    border-bottom: none;
}

.main-nav ul ul a {
    padding: 12px 18px;
    font-size: 0.95em;
    border-left: none;
    transition: background-color 0.2s ease;
}

.main-nav ul ul a:hover {
    background: #0057B7;
}

/* === MOBILE HAMBURGER MENU ========================================== */
.nav-toggle {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 1.8rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    position: absolute;
    right: 15px;
    z-index: 1002;
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* === MOBILE RESPONSIVE STYLES ======================================= */
@media (max-width: 900px) {
    /* Show hamburger button and position it below rolling banner */
    .nav-toggle {
        display: block;
        top: calc(2.5em + 20px);
    }

    /* Hide menu by default on mobile */
    .main-nav ul#primary-menu {
        display: none !important;
        flex-direction: column;
        position: fixed;
        top: calc(2.5em + 60px);
        left: 0;
        width: 100%;
        background: #001B44; /* Dark blue from tartan header */
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        max-height: calc(100vh - 2.5em - 60px);
        overflow-y: auto;
        z-index: 1001;
        transition: transform 0.3s ease-out;
        transform: translateY(-100%);
    }

    /* Show menu when nav-open class is applied */
    .main-nav.nav-open ul#primary-menu {
        display: flex !important;
        transform: translateY(0);
    }

    /* Main menu items */
    .main-nav .menu-item {
        border-left: none;
        border-bottom: 1px solid #003F8F; /* Slightly lighter blue for borders */
        width: 100%;
    }

    .main-nav .menu-item:last-child {
        border-bottom: none;
    }

    .main-nav a {
        padding: 15px 20px;
        text-align: left;
        width: 100%;
        color: #fff;
        background: #001B44; /* Ensure consistent background */
        transition: background-color 0.2s ease;
    }

    .main-nav a:hover {
        background: #003F8F; /* Lighter blue for hover */
    }

    /* Submenu styling - hidden by default */
    .main-nav ul ul {
        position: static;
        background: #002B5C; /* Slightly lighter than parent for hierarchy */
        border-radius: 0;
        box-shadow: none;
        border-top: none;
        min-width: auto;
        display: none !important; /* Force hide by default */
        transform: none !important;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    /* Show submenu only when parent is explicitly open */
    .has-submenu.open > ul {
        display: block !important;
        opacity: 1;
    }

    .main-nav ul ul a {
        padding-left: 40px;
        font-size: 0.9em;
        background: #002B5C; /* Match parent submenu background */
    }

    .main-nav ul ul a:hover {
        background: #003F8F; /* Lighter blue for hover */
    }

    /* Mobile accordion indicators */
    .has-submenu > a::after {
        content: ' ▸';
        float: right;
        transition: transform 0.2s;
    }

    .has-submenu.open > a::after {
        transform: rotate(90deg);
    }

    /* Ensure submenus are hidden on initial load */
    .main-nav ul ul {
        display: none !important;
    }

    /* Hide desktop-only items on mobile */
    .desktop-only {
        display: none;
    }
}

/* === LEGACY SUPPORT ================================================== */
/* Support for existing custom pages dropdown */
.custom-pages-dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #003F8F;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    top: 100%;
    left: 0;
    border-radius: 0 0 4px 4px;
}

.dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.dropdown-content a:hover {
    background-color: #0057B7;
}

.custom-pages-dropdown:hover .dropdown-content {
    display: block;
}

/* ============================= TEAM GRID STYLES ============================= */
.team-grid {
    padding: 2rem 0;
}

.team-grid .team-members {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.team-member {
    background: rgba(255, 255, 255, 0.9);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.team-member .tm-title {
    color: #0057B7;
    font-size: 1.2em;
    font-weight: 700;
    margin-bottom: 1rem;
    order: -1; /* Places title above image */
}

.team-member .avatar {
    width: 150px;
    height: 150px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #0057B7;
    transition: transform 0.3s ease;
}

.team-member .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.team-member:hover .avatar img {
    transform: scale(1.05);
}

.team-member .tm-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.team-member .tm-bio {
    color: #333;
    line-height: 1.5;
    margin: 0;
}

.team-member .tm-quote {
    position: relative;
    padding: 0 1em;
}

.team-member .tm-quote::before {
    content: '"';
    position: absolute;
    left: -0.25em;
    top: -0.25em;
    font-size: 1.2em;
    color: var(--accent-color, #666);
}

.team-member .tm-quote::after {
    content: '"';
    position: absolute;
    right: -0.25em;
    bottom: -0.25em;
    font-size: 1.2em;
    color: var(--accent-color, #666);
}

.team-grid .no-members {
    grid-column: 1 / -1;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .team-grid .team-members {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .team-member {
        padding: 1rem;
    }

    .team-member .avatar {
        width: 120px;
        height: 120px;
    }

    .team-member .tm-title {
        font-size: 1.1em;
    }
}

@media (max-width: 480px) {
    .team-grid .team-members {
        grid-template-columns: 1fr;
    }

    .team-member .avatar {
        width: 100px;
        height: 100px;
    }
}

/* ================= DYNAMIC WIDTH CONSTRAINT =============== */
.dynamic-section-container,
.dyn-block {
    max-width: 920px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    overflow-x: hidden; /* Prevent horizontal scroll on very narrow screens */
}

/* ================= SAMSUNG NESTED ELEMENTS FIX =============== */
@media (max-width: 768px) and (orientation: portrait) {
    .fix-nested {
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }
}

@media (max-width: 768px) and (orientation: landscape) {
    .fix-nested {
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }
}

/* ================= TEAM MEMBER CENTERING =============== */
@media (max-width: 480px) and (orientation: portrait) {
    .team-grid .team-members {
        justify-content: center;
        grid-template-columns: minmax(280px, 1fr);
    }
    
    .team-member {
        margin-left: auto;
        margin-right: auto;
        width: 100%;
        max-width: 320px; /* Prevent cards from getting too wide */
    }
}

/* ================= SOCIAL MEDIA FOOTER =============== */
@media (max-width: 768px) {
    .floating-footer {
        display: flex;
        justify-content: center;
        gap: 1.2rem;
        padding: 0.5rem;
        width: 100%;
    }

    .floating-footer .soc {
        margin: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Fallback for older browsers */
    .floating-footer .soc + .soc {
        margin-left: 1.2rem !important;
    }
}

/* ================= LOGIN FORM CENTERING =============== */
.login-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem 1rem;
    box-sizing: border-box;
}

@media (max-width: 767px) {
    .login-wrapper {
        display: block;
        min-height: auto;
        padding: 1.5rem 1rem;
    }
}
