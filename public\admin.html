<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>TAS Admin – Dashboard</title>

    <!-- favicon & css -->
    <link rel="icon" href="/images/bannerShield2.png" type="image/png" />
    <link rel="stylesheet" href="/styles2.css" />
    <link rel="stylesheet" href="/header-banner.css" />
    <link rel="stylesheet" href="/css/nav.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <script src="/responsive-helper.js" defer></script>
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>

    <style>
        /* lilac panel used by both forms */
        .admin-form-container {
            max-width: 700px;
            margin: 40px auto;
            background: #C8A2C8;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,.1)
        }

            .admin-form-container h3 {
                margin-top: 0
            }

        form label {
            display: block;
            margin-top: 14px;
            font-weight: bold
        }

        form input, form textarea, form select {
            width: 100%;
            padding: 9px;
            margin-top: 4px;
            border: 1px solid #ccc;
            border-radius: 4px
        }

        form button {
            margin-top: 20px;
            padding: 12px 20px;
            background: #0057B7;
            color: #fff;
            border: 0;
            border-radius: 4px;
            font-size: 1.05em;
            cursor: pointer;
            transition: .25s
        }

            form button:hover {
                background: #0046a5;
                box-shadow: 0 0 8px #C8A2C8
            }

        /* Style for the cancel button */
        form button.cancel-btn { background: #6c757d; margin-left: 10px; }
        form button.cancel-btn:hover { background: #5a6268; }

        /* small table that lists existing dynamic sections */
        table.admin-table {
            width: 100%;
            margin-top: 25px;
            border-collapse: collapse;
            font-size: .93em
        }

        .admin-table th, .admin-table td {
            border: 1px solid #ddd;
            padding: 6px
        }

        .admin-table th {
            background: #ececec;
            text-align: left
        }

        .admin-table button {
            background: none;
            border: 0;
            font-size: 1.2em;
            cursor: pointer;
            margin: 0 5px;
            padding: 0;
        }

            .admin-table button:hover {
                color: #b00
            }

        /* Tabs styling */
        .tabs-container {
            max-width: 700px;
            margin: 20px auto;
        }

        .tab-buttons {
            display: flex;
            margin-bottom: 0;
        }

        .tab-button {
            flex: 1;
            padding: 12px 20px;
            background: #E6F0FF;
            color: #0057B7;
            border: 1px solid #0057B7;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            font-size: 1.05em;
            cursor: pointer;
            transition: .25s;
            text-align: center;
            font-weight: bold;
        }

        .tab-button.active {
            background: #0057B7;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Tutor table specific styles */
        #tutorTable img {
            max-width: 60px;
            max-height: 60px;
            border-radius: 4px;
        }

        .tutor-subjects {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .tutor-subjects:hover {
            white-space: normal;
            overflow: visible;
        }
    </style>
</head>

<body data-page="admin">
    <!-- ─────────────── HEADER/BANNER ─────────────── -->
    <header>
        <h1>Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a class="banner-login-link login-box" href="/">Home</a>
            <a class="banner-login-link login-box" href="/login.html?role=admin">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner"></div>
    </div>

    <!-- ─────────────── MAIN CONTENT ─────────────── -->
    <main>
        <!-- Tabs Navigation -->
        <div class="tabs-container">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="content-tab">Manage Content</button>
                <button class="tab-button" data-tab="tutors-tab">Manage Tutors</button>
            </div>
        </div>

        <!-- Content Tab -->
        <div id="content-tab" class="tab-content active">
            <!-- ░░░░░░  ADD‑/DELETE SECTION  ░░░░░░ -->
            <div class="admin-form-container">
                <!-- Heading will change dynamically -->
                <h3 id="sectionFormHeading">Add a Dynamic Section</h3>

                <!-- ★ NEW – simplified form : only page, heading, paragraph, image ★ -->
                <form id="addSection" enctype="multipart/form-data" autocomplete="off">
                    <label>
                        Target Page:
                        <select name="page" id="pageSelect">
                            <!-- Generated dynamically from pages.js -->
                        </select>
                        <a id="viewPageLink" href="/" target="_blank" style="margin-left:12px;font-size:.9em">🔗 Open Page</a>
                    </label>

                    <label>Layout:
                        <select name="layout" id="sectionLayout">
                            <option value="standard" selected>Standard</option>
                            <option value="team">Team Members Grid</option>
                        </select>
                    </label>

                    <!-- ★ Navigation integration controls (always visible) -->
                    <label style="display:flex; align-items:center;">
                        <input type="checkbox" name="showInNav" id="showInNav" style="width:auto; margin-right:8px;">
                        <span>Add a link to the navigation bar</span>
                    </label>

                    <label id="navCatRow" style="margin-top:10px; display:none;">
                        Show under:
                        <select name="navCategory" id="sectionNavCategory">
                            <option value="tutors">For Tutors</option>
                            <option value="parents">For Parents</option>
                            <option value="about" selected>About TAS</option>
                        </select>
                    </label>

                    <div id="standardFields">
                        <label>Heading:<input name="heading" required></label>
                        <label>Paragraph:<textarea name="text" rows="3" required></textarea></label>
                        <label>Image:<input type="file" name="image" id="sectionImage" accept="image/*"></label>
                        <!-- To show current image and allow removal -->
                        <div id="currentImagePreview" style="margin-top: 10px; display: none;">
                            <p style="margin-bottom: 5px;">Current Image:</p>
                            <img src="" alt="Current Image" style="max-width: 100px; max-height: 100px; border-radius: 4px;">
                            <label style="display: inline-flex; align-items: center; margin-left: 15px;">
                                <input type="checkbox" name="removeImage" value="true" style="width: auto; margin-right: 5px;"> Remove Image
                            </label>
                        </div>

                        <!-- ★ NEW: Add button fields -->
                        <hr style="border-top: 1px solid #b3a1b3; margin: 20px 0;">
                        <label>Button Label (Optional): <input name="buttonLabel" placeholder="e.g. Learn More"></label>
                        <label>Button URL (Optional): <input name="buttonUrl" type="url" placeholder="https://example.com"></label>

                        <!-- ★ NEW: Checkbox to remove button, shown only during edit -->
                        <label id="removeButtonRow" style="display:none; align-items:center; margin-top: 10px;">
                          <input type="checkbox" name="removeButton" value="true" style="width:auto; margin-right:8px;">
                          Remove Button
                        </label>


                    </div>

                    <!-- Team builder fields -->
                    <div id="teamBuilder" style="display:none; margin-top:15px;">
                        <label>Section Heading: <input name="teamHeading" placeholder="e.g. Our Team"></label>
                        <button type="button" id="addMemberBtn">➕ Add Team Member</button>
                        <div id="teamMemberList"></div>
                        <input type="hidden" name="team" id="teamData">
                    </div>

                    <div id="rollingBannerFields" style="display: none;">
                        <label>News Item:<input name="rollingHeading" value="Rolling News" readonly></label>
                        <label>News Content:<textarea name="rollingText" rows="3" required placeholder="Enter news content to display in the rolling banner"></textarea></label>
                        <!-- No image field for rolling banner -->
                    </div>

                    <label>
                        Position on Page:
                        <select name="position">
                            <option value="top">Top of Page</option>
                            <option value="middle">Middle of Page</option>
                            <option value="bottom" selected>Bottom of Page</option>
                        </select>
                    </label>

                    <!-- Buttons will be managed by JS -->
                    <button type="submit" id="submitSectionBtn">Add Section</button>
                    <button type="button" id="cancelEditBtn" class="cancel-btn" style="display: none;">Cancel Edit</button>
                </form>

                <!-- ★ NEW – table of existing sections with a delete button ★ -->
                <h3 style="margin-top:35px">Existing sections for this page</h3>
                <table id="sectionTable" class="admin-table">
                    <thead>
                        <tr><th>#</th><th>Heading</th><th>Layout</th><th>Position</th><th>Image?</th><th>Button?</th><th>Actions</th></tr>
                    </thead>
                    <tbody><!-- filled by JS --></tbody>
                </table>
            </div>

            <!-- ░░░░░░  CREATE NEW PAGE  ░░░░░░ -->
            <div class="admin-form-container">
                <h3 id="pageFormHeading">Create a New Page</h3>

                <form id="pageForm" enctype="multipart/form-data" autocomplete="off">
                    <label>
                        Page URL (e.g., "summer-tutoring"):
                        <input type="text" name="slug" id="pageSlug" required>
                    </label>

                    <label>Page Title:<input name="heading" required></label>
                    <label>Page Content:<textarea name="text" rows="6" required></textarea></label>

                    <label>
                        Show in Navigation Menu:
                        <select name="navCategory" id="pageNavCategory">
                            <option value="tutors">For Tutors</option>
                            <option value="parents">For Parents</option>
                            <option value="about" selected>About TAS</option>
                        </select>
                    </label>

                    <!-- ★ NEW: Add button fields -->
                    <hr style="border-top:1px solid #b3a1b3;margin:20px 0">
                    <label>
                      Button Label (optional):
                      <input name="buttonLabel" placeholder="e.g. Get Started">
                    </label>
                    <label>
                      Button URL (optional):
                      <input type="url" name="buttonUrl" placeholder="https://…">
                    </label>

                    <label>Featured Image:<input type="file" name="image" id="pageImage" accept="image/*"></label>

                    <div id="currentPageImagePreview" style="display:none;margin-top:10px"></div>

                    <label>
                        <input type="checkbox" name="isPublished" id="isPublished" checked>
                        Publish page immediately
                    </label>

                    <!-- shown only while editing -->
                    <div id="pageEditOptions" style="display:none;margin-top:10px">
                      <label style="display:flex;align-items:center">
                        <input type="checkbox" name="removeButton" value="true" style="width:auto;margin-right:8px">
                        Remove Button
                      </label>
                      <label style="display:flex;align-items:center;margin-top:5px">
                        <input type="checkbox" name="removeImage" value="true" style="width:auto;margin-right:8px">
                        Remove Image
                      </label>
                    </div>

                    <button type="submit" id="submitPageBtn">Create Page</button>
                    <button type="button" id="cancelPageEditBtn" class="cancel-btn" style="display:none">Cancel Edit</button>
                </form>

                <h3 style="margin-top:35px">Existing Pages</h3>
                <table id="pagesTable" class="admin-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>URL</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody><!-- filled by JS --></tbody>
                </table>
            </div>
        </div>

        <!-- Tutors Tab -->
        <div id="tutors-tab" class="tab-content">
            <!-- ░░░░░░ ADD‑TUTOR FORM ░░░░░░ -->
            <div class="admin-form-container">
                <h3>Add a Tutor</h3>

                <!-- handled completely in JS  -->
                <form id="tutorForm" autocomplete="off">
                    <label>Tutor Name:<input type="text" name="name" required /></label>

                    <label>
                        Subjects (comma‑separated):
                        <input type="text" name="subjects" required />
                    </label>

                    <label>
                        Cost Range (e.g. __P__ for £, __P____P__ for ££):
                        <input type="text" name="costRange" required />
                    </label>

                    <label>
                        Badges (comma‑separated):
                        <input type="text" name="badges" />
                    </label>

                    <label>
                        Tutor Image:
                        <input type="file" id="imageField" accept="image/*" />
                    </label>

                    <label>
                        Contact (email or website):
                        <input type="text" name="contact" />
                    </label>

                    <label>Description:<textarea name="description"></textarea></label>

                    <label>
                        Postcodes (comma‑separated):
                        <input type="text" name="postcodes" />
                    </label>

                    <button type="submit">Add Tutor</button>
                </form>
            </div>

            <!-- ░░░░░░ MANAGE TUTORS ░░░░░░ -->
            <div class="admin-form-container">
                <h3>Manage Existing Tutors</h3>
                <p>Click on a tutor's name to view details. Use the delete button to remove a tutor.</p>

                <table id="tutorTable" class="admin-table">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Subjects</th>
                            <th>Cost</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Filled by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- ───────────────  MODULE SCRIPT  ─────────────── -->
    <script type="module">
        import { uploadImage } from '/js/upload-helper.js';
        import { PAGES } from '/js/pages.js';

        /* ── page‑guard: only admins allowed ───────────────────────── */
        const auth = await fetch('/api/protected?role=admin');
        if (!auth.ok) { location.href = '/login.html?role=admin'; throw 0; }

        /* ── rolling banner text ──────────────────────────────────── */
        // First try to fetch news from sections API
        fetch('/api/sections?page=rolling-banner')
            .then(r => r.json())
            .then(sections => {
                if (sections && sections.length > 0) {
                    // Format news information - join all text content with separator
                    const text = sections.map(s => s.text).join(' | ');
                    tutorBanner.textContent = text;
                } else {
                    // Fallback to tutors if no news sections are found
                    return fetch('/api/tutors?format=json')
                        .then(r => r.json())
                        .then(list => {
                            tutorBanner.textContent =
                                list.map(t => `${t.name} (${t.subjects.join(', ')})`).join(' | ');
                        });
                }
            })
            .catch(console.error);

        /* ── tab switching functionality ──────────────────────────── */
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        /* ░░░ 1. ADD‑TUTOR WORKFLOW  ░░░ */
        tutorForm.addEventListener('submit', async e => {
            e.preventDefault();
            const fd = new FormData(tutorForm);
            const csv = s => s.split(',').map(x => x.trim()).filter(Boolean);

            /* optional image upload */
            let imagePath = '';
            if (imageField.files[0]) {
                const f = imageField.files[0];
                if (f.size > 2 * 1024 * 1024) return alert('Image > 2 MB');
                try { imagePath = await uploadImage(f, 'tutors'); }
                catch (err) { return alert(err.message); }
            }

            const payload = {
                name: fd.get('name').trim(),
                subjects: csv(fd.get('subjects')),
                costRange: fd.get('costRange').trim(),
                badges: csv(fd.get('badges')),
                contact: fd.get('contact').trim(),
                description: fd.get('description').trim(),
                postcodes: csv(fd.get('postcodes')),
                imagePath
            };

            try {
                console.log('Sending tutor data to API:', payload);
                const r = await fetch('/api/addTutor', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload),
                    credentials: 'include' // Important: Include cookies with the request
                });

                if (!r.ok) {
                    // Try to parse the error response as JSON
                    try {
                        const errorData = await r.json();
                        console.error('Error adding tutor:', errorData);
                        return alert(`Error adding tutor: ${errorData.message || 'Unknown error'}`);
                    } catch (parseError) {
                        // If JSON parsing fails, use text response
                        const errorText = await r.text();
                        console.error('Error adding tutor (text):', errorText);
                        return alert(`Error adding tutor: ${errorText || 'Unknown error'}`);
                    }
                }

                console.log('Tutor added successfully');
                alert('Tutor added successfully!');
                tutorForm.reset();
                loadTutors(); // Refresh the tutor list
            } catch (error) {
                console.error('Exception while adding tutor:', error);
                alert(`An error occurred: ${error.message}`);
            }
        });

        /* ░░░ LOAD AND DELETE TUTORS ░░░ */
        // Function to load tutors
        async function loadTutors() {
            try {
                const response = await fetch('/api/tutors?format=json');
                if (!response.ok) {
                    throw new Error('Failed to fetch tutors');
                }

                const tutors = await response.json();
                const tutorTableBody = document.querySelector('#tutorTable tbody');
                tutorTableBody.innerHTML = '';

                tutors.forEach(tutor => {
                    const row = document.createElement('tr');

                    // Image cell
                    const imageCell = document.createElement('td');
                    if (tutor.imagePath) {
                        const img = document.createElement('img');
                        img.src = tutor.imagePath;
                        img.alt = tutor.name;
                        imageCell.appendChild(img);
                    } else {
                        imageCell.textContent = 'No image';
                    }
                    row.appendChild(imageCell);

                    // Name cell
                    const nameCell = document.createElement('td');
                    nameCell.textContent = tutor.name;
                    row.appendChild(nameCell);

                    // Subjects cell
                    const subjectsCell = document.createElement('td');
                    subjectsCell.className = 'tutor-subjects';
                    subjectsCell.textContent = tutor.subjects.join(', ');
                    row.appendChild(subjectsCell);

                    // Cost cell
                    const costCell = document.createElement('td');
                    costCell.textContent = tutor.costRange;
                    row.appendChild(costCell);

                    // Actions cell
                    const actionsCell = document.createElement('td');
                    const deleteButton = document.createElement('button');
                    deleteButton.innerHTML = '🗑️';
                    deleteButton.title = 'Delete Tutor';
                    deleteButton.onclick = () => deleteTutor(tutor._id);
                    actionsCell.appendChild(deleteButton);
                    row.appendChild(actionsCell);

                    tutorTableBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error loading tutors:', error);
                alert('Failed to load tutors. Please try again.');
            }
        }

        // Function to delete a tutor
        async function deleteTutor(tutorId) {
            if (!confirm('Are you sure you want to delete this tutor?')) {
                return;
            }

            try {
                const response = await fetch(`/api/addTutor?id=${tutorId}`, {
                    method: 'DELETE',
                    credentials: 'include' // Include cookies for authentication
                });

                if (response.ok) {
                    alert('Tutor deleted successfully!');
                    loadTutors(); // Refresh the tutor list
                } else {
                    const error = await response.text();
                    alert(`Failed to delete tutor: ${error}`);
                }
            } catch (error) {
                console.error('Error deleting tutor:', error);
                alert('An error occurred while deleting the tutor.');
            }
        }

        // Load tutors when the page loads
        loadTutors();

        /* ░░░ 2. ADD / EDIT / DELETE SECTIONS ░░░ */
        const pageSel            = document.getElementById('pageSelect');
        const viewPageLink       = document.getElementById('viewPageLink');
        const sectionTbody       = document.querySelector('#sectionTable tbody');
        const addSectionForm     = document.getElementById('addSection');
        const sectionFormHeading = document.getElementById('sectionFormHeading');
        const submitSectionBtn   = document.getElementById('submitSectionBtn');
        const cancelEditBtn      = document.getElementById('cancelEditBtn');
        const currentImagePreview= document.getElementById('currentImagePreview');
        const removeButtonRow    = document.getElementById('removeButtonRow');

        /* ── populate dropdown from the central list ───────────────────────── */
        pageSel.innerHTML = PAGES.map(p => `<option value="${p}">${p}</option>`).join('');

        /* ── helper: keep 🔗 link consistent ────────────────────────────────── */
        function updatePageLink(slug) {
          if (slug === 'rolling-banner') {
            viewPageLink.style.display = 'none';
          } else {
            viewPageLink.href = slug === 'index' ? '/' : `/${slug}.html`;
            viewPageLink.style.display = 'inline-block';
          }
        }

        /* ── helper: reset form but KEEP page selection ────────────────────── */
        function resetSectionForm() {
          const slug = pageSel.value;        // remember
          addSectionForm.reset();            // native reset (clears <select> too)
          pageSel.value = slug;              // restore

          delete addSectionForm.dataset.editId;
          sectionFormHeading.textContent = 'Add a Dynamic Section';
          submitSectionBtn.textContent   = 'Add Section';
          cancelEditBtn.style.display    = 'none';
          currentImagePreview.style.display = 'none';
          document.querySelector('input[name="removeImage"]').checked = false;

          // Add reset logic for button fields
          document.querySelector('[name="buttonLabel"]').value = '';
          document.querySelector('[name="buttonUrl"]').value   = '';
          document.querySelector('[name="removeButton"]').checked = false;
          removeButtonRow.style.display = 'none';

          // Add reset logic for team fields
          teamBuilder.style.display = 'none';
          teamMemberList.innerHTML = '';
          teamMembers = [];
          teamData.value = '[]';

          toggleFields();                    // (function already defined earlier)
          updatePageLink(slug);
        }

        /* ── show / hide fields & toggle HTML-5 "required" attrs ───────────── */
        function toggleFields() {
            const standardFields = document.getElementById('standardFields');
            const rollingBannerFields = document.getElementById('rollingBannerFields');
            const positionSelect = document.querySelector('select[name="position"]');

            const headingInput = document.querySelector('input[name="heading"]');
            const textTextarea = document.querySelector('textarea[name="text"]');
            const rollingTextarea = document.querySelector('textarea[name="rollingText"]');

            const isBanner = pageSel.value === 'rolling-banner';

            /* show / hide blocks ------------------------------------------------ */
            standardFields.style.display = isBanner ? 'none' : 'block';
            rollingBannerFields.style.display = isBanner ? 'block' : 'none';
            if (positionSelect) {
                positionSelect.parentElement.style.display = isBanner ? 'none' : 'block';
            }

            /* HTML-5 "required" attributes -------------------------------------- */
            if (isBanner) {
                headingInput.removeAttribute('required');
                textTextarea.removeAttribute('required');
                rollingTextarea.setAttribute('required', '');
            } else {
                headingInput.setAttribute('required', '');
                textTextarea.setAttribute('required', '');
                rollingTextarea.removeAttribute('required');
                /* FULL reset of the hidden team-builder so no zombie <input type="file"> remains */
                teamMemberList.innerHTML = '';    // ← this was the missing line
                teamMembers =[];
                teamData.value = '[]';
            }
        }


        /* ── fetch & render sections for current slug ──────────────────────── */
        let currentSections = [];            // cache for edit/delete clicks

        async function loadSections() {
          const r = await fetch(`/api/sections?page=${pageSel.value}`);
          if (!r.ok) return alert(await r.text());

          currentSections      = await r.json();
          sectionTbody.innerHTML = '';

          currentSections.forEach((s, i) => {
            const pos = { top:'🔝 Top', middle:'⏺️ Middle', bottom:'🔽 Bottom' }[s.position||'bottom'];
            const layout = s.layout === 'team' ? 'Team Grid' : 'Standard';

            // For team sections, check if any team members have images
            let hasImage = s.image ? true : false;
            if (s.layout === 'team' && s.team && Array.isArray(s.team)) {
                hasImage = s.team.some(member => member.image);
            }

            sectionTbody.insertAdjacentHTML('beforeend', `
              <tr>
                <td>${i+1}</td><td>${s.heading}</td><td>${layout}</td><td>${pos}</td><td>${hasImage?'✔︎':''}</td><td>${s.buttonLabel?'✔︎':''}</td>
                <td>
                  <button class="edit"   data-id="${s._id}" title="Edit">✏️</button>
                  <button class="delete" data-id="${s._id}" title="Delete">🗑️</button>
                </td>
              </tr>`);
          });
          toggleFields();
        }

        /* ── dropdown change ───────────────────────────────────────────────── */
        pageSel.addEventListener('change', () => {
          loadSections();
          resetSectionForm();                // preserves the just-chosen slug
          updatePageLink(pageSel.value);
        });

        /* ── cancel button ─────────────────────────────────────────────────── */
        cancelEditBtn.addEventListener('click', resetSectionForm);

        /* ── navigation controls logic ──────────────────────────────────────── */
        const showInNav = document.getElementById('showInNav');
        const navCatRow = document.getElementById('navCatRow');
        const sectionNavCategory = document.getElementById('sectionNavCategory');

        // Show/hide navigation category selector based on checkbox
        showInNav.addEventListener('change', () => {
            navCatRow.style.display = showInNav.checked ? 'block' : 'none';
        });

        /* ── layout controls logic ──────────────────────────────────────────── */
        const sectionLayout = document.getElementById('sectionLayout');
        const standardFields = document.getElementById('standardFields');
        const teamBuilder = document.getElementById('teamBuilder');
        const addMemberBtn = document.getElementById('addMemberBtn');
        const teamMemberList = document.getElementById('teamMemberList');
        const teamData = document.getElementById('teamData');

        let teamMembers = [];

        // Show / hide blocks & (un)set required attrs whenever layout flips
        sectionLayout.addEventListener('change', () => {
            const isTeam = sectionLayout.value === 'team';

            // 1. toggle visibility
            standardFields.style.display = isTeam ? 'none' : 'block';
            teamBuilder.style.display = isTeam ? 'block' : 'none';

            // 2. section-level heading inputs
            const headingInput = standardFields.querySelector('[name="heading"]');
            const textTextarea = standardFields.querySelector('[name="text"]');
            const teamHeadingInput = teamBuilder.querySelector('[name="teamHeading"]');

            if (isTeam) {
                headingInput.removeAttribute('required');
                textTextarea.removeAttribute('required');
                teamHeadingInput.setAttribute('required', '');
            } else {
                headingInput.setAttribute('required', '');
                textTextarea.setAttribute('required', '');
                teamHeadingInput.removeAttribute('required');
            }

            // 3. card-level fields (name + bio)
            teamMemberList
                .querySelectorAll('.member-nameRole, .member-bio')
                .forEach(el => isTeam
                    ? el.setAttribute('required', '')
                    : el.removeAttribute('required'));
        });



        // Add team member functionality
        function createTeamMemberCard(index) {
            const el = document.createElement('div');
            el.className = 'team-member-form';
            el.style.cssText = 'border:1px solid #ddd;padding:15px;margin:10px 0;border-radius:5px;background:#f9f9f9';

            el.innerHTML = `
                <h4>Team Member ${index + 1}</h4>
                
                <div class="form-group">
                    <label>Name & Role:
                        <input type="text" 
                               class="member-nameRole form-control" 
                               placeholder="e.g. Karen Simpson – Director" 
                               required>
                    </label>
                </div>

                <div class="form-group">
                    <label>Bio:
                        <textarea class="member-bio form-control" 
                                  rows="3" 
                                  required></textarea>
                    </label>
                </div>

                <div class="form-group">
                    <label>Quote (optional):
                        <textarea class="member-quote form-control" 
                                  rows="2" 
                                  placeholder='"Tutoring changes lives…"'></textarea>
                    </label>
                </div>

                <div class="form-group">
                    <label>Image:
                        <input type="file" 
                               class="member-image form-control" 
                               accept="image/*">
                        <div class="image-preview" style="margin-top:10px;max-width:200px;display:none">
                            <img src="" alt="Preview" style="width:100%;border-radius:4px">
                        </div>
                    </label>
                </div>

                <button type="button" 
                        class="remove-member btn btn-danger" 
                        style="margin-top:10px">
                    Remove Member
                </button>
            `;

            // Image preview functionality
            const imageInput = el.querySelector('.member-image');
            const previewDiv = el.querySelector('.image-preview');
            const previewImg = previewDiv.querySelector('img');

            imageInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    // Reset upload flag when new file is selected
                    el.dataset.imageUploaded = '';
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        previewImg.src = e.target.result;
                        previewDiv.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                } else {
                    previewDiv.style.display = 'none';
                }
                updateTeamData();
            });

            // Remove button handler
            el.querySelector('.remove-member').addEventListener('click', () => {
                if (confirm('Are you sure you want to remove this team member?')) {
                    el.remove();
                    updateTeamData();
                }
            });

            // Field change handlers
            el.querySelectorAll('input, textarea').forEach(input => {
                input.addEventListener('input', updateTeamData);
                input.addEventListener('change', updateTeamData);
            });

            return el;
        }

        function addTeamMember() {
            const card = createTeamMemberCard(teamMemberList.children.length);
            teamMemberList.appendChild(card);
            updateTeamData();
        }

        async function updateTeamData() {
            teamMembers = [];          // reset

            for (const form of teamMemberList.querySelectorAll('.team-member-form')) {
                const name = form.querySelector('.member-nameRole').value.trim();
                const bio = form.querySelector('.member-bio').value.trim();
                const quote = form.querySelector('.member-quote').value.trim();
                const file = form.querySelector('.member-image').files[0];

                // skip incomplete cards
                if (!name || !bio) continue;

                // existing / newly-uploaded picture
                let imagePath = form.dataset.existingImage || '';

                // 👇 guard against multiple concurrent uploads
                if (file && !form.dataset.imageUploaded && !form.dataset.uploading) {
                    form.dataset.uploading = 'true';
                    try {
                        imagePath = await uploadImage(file, 'team');
                        form.dataset.imageUploaded = 'true';
                        form.dataset.existingImage = imagePath;
                    } catch (err) {
                        console.error('Image upload failed:', err);
                        alert('Could not upload image – please try again.');
                    } finally {
                        delete form.dataset.uploading;   // always clear
                    }
                }

                teamMembers.push({ name, bio, quote, image: imagePath });
            }

            teamData.value = JSON.stringify(teamMembers);
            console.log('Team data updated', teamMembers);
        }


        // Wire up the add member button
        addMemberBtn.addEventListener('click', addTeamMember);

        /* ── initial boot ──────────────────────────────────────────────────── */
        await loadSections();
        pageSel.dispatchEvent(new Event('change'));   // sets initial link

        // Handles form submission for both ADD and UPDATE
        addSectionForm.addEventListener('submit', async e => {
            e.preventDefault();
            const fd = new FormData(addSectionForm);

            // Add navigation fields
            fd.set('showInNav', showInNav.checked ? 'true' : 'false');
            fd.set('navCategory', sectionNavCategory.value || 'about');

            // Add layout field
            fd.set('layout', sectionLayout.value || 'standard');

            // Handle team layout
            if (sectionLayout.value === 'team') {
                await updateTeamData(); // Ensure team data is current

                if (!teamData.value || teamData.value === '[]') {
                    return alert('Please add at least one team member');
                }

                fd.set('heading', document.querySelector('[name="teamHeading"]').value);
                fd.set('text', 'Team members section'); // Placeholder text
                fd.set('team', teamData.value);
            }

            const editId = addSectionForm.dataset.editId;
            if (editId) {
                fd.append('editId', editId);
            }

            /* rolling-banner special-case */
            if (pageSel.value === 'rolling-banner') {
                const txt = fd.get('rollingText').trim();
                if (!txt) return alert('Please enter news content for the rolling banner');

                fd.set('heading', fd.get('rollingHeading') || 'Rolling News');
                fd.set('text', txt);
                fd.delete('rollingHeading'); fd.delete('rollingText');
            }

            /* optional image upload (skip for rolling-banner) */
            if (pageSel.value !== 'rolling-banner') {
                const img = fd.get('image');
                if (img instanceof File && img.size) {
                    try {
                        const url = await uploadImage(img, 'sections');
                        fd.set('imagePath', url);        // server expects this field
                    } catch (err) { return alert(err.message); }
                }
            }
            fd.delete('image');                    // never send raw file

            /* DEBUG – inspect payload in browser console
               -------------------------------------------------
               for (const [k,v] of fd.entries()) console.log('FD',k,v);
            */

            // Send to the unified POST endpoint
            const r = await fetch('/api/sections', { method: 'POST', body: fd });
            if (!r.ok) { return alert(await r.text()); }

            alert(`Section ${editId ? 'updated' : 'added'} successfully!`);
            resetSectionForm();
            loadSections();

            /* refresh banner immediately if we just added news */
            if (pageSel.value === 'rolling-banner') {
                fetch('/api/sections?page=rolling-banner')
                    .then(r => r.json())
                    .then(list => tutorBanner.textContent = list.map(s => s.text).join(' | '))
                    .catch(console.error);
            }
        });

        // Handles clicks on EDIT and DELETE buttons
        sectionTbody.addEventListener('click', async e => {
            const id = e.target.dataset.id;
            if (!id) return;

            // Handle DELETE
            if (e.target.classList.contains('delete')) {
                if (!confirm('Are you sure you want to delete this section?')) return;
                const r = await fetch(`/api/sections?id=${id}`, { method: 'DELETE' });
                if (r.ok) {
                    if (id === addSectionForm.dataset.editId) resetSectionForm();
                    loadSections();
                } else {
                    alert(await r.text());
                }
                return; // Stop execution after delete
            }

            // Handle EDIT
            if (e.target.classList.contains('edit')) {
                const section = currentSections.find(s => s._id === id);
                if (!section) return alert('Section not found. Please refresh.');

                // ---- Set state and trigger UI update ----
                addSectionForm.dataset.editId = id;
                sectionLayout.value = section.layout || 'standard';
                sectionLayout.dispatchEvent(new Event('change')); // This shows/hides the correct field blocks

                // ---- Populate form based on layout ----
                if (section.layout === 'team') {
                    sectionFormHeading.textContent = 'Edit Team Section'; // UI Nicety
                    teamBuilder.querySelector('[name="teamHeading"]').value = section.heading || '';

                    // Rebuild team member cards
                    teamMemberList.innerHTML = '';
                    teamMembers = [];

                    (section.team || []).forEach((member, index) => {
                        const card = createTeamMemberCard(teamMemberList.children.length);

                        // Pre-fill fields from the section data
                        card.querySelector('.member-nameRole').value = member.name || '';
                        card.querySelector('.member-bio').value = member.bio || '';
                        card.querySelector('.member-quote').value = member.quote || '';

                        // Remember and display the existing image
                        if (member.image) {
                            card.dataset.existingImage = member.image; // This prevents the image from being lost on update
                            const previewDiv = card.querySelector('.image-preview');
                            const previewImg = previewDiv.querySelector('img');
                            previewImg.src = member.image;
                            previewDiv.style.display = 'block';
                        }
                        teamMemberList.appendChild(card);
                    });
                    updateTeamData(); // Sync the populated data to the hidden input

                } else { // It's a 'standard' layout
                    sectionFormHeading.textContent = 'Edit Dynamic Section'; // UI Nicety
                    addSectionForm.querySelector('[name="heading"]').value = section.heading;
                    addSectionForm.querySelector('[name="text"]').value = section.text;

                    if (section.image) {
                        currentImagePreview.querySelector('img').src = section.image;
                        currentImagePreview.style.display = 'block';
                    } else {
                        currentImagePreview.style.display = 'none';
                    }
                    document.querySelector('input[name="removeImage"]').checked = false;

                    addSectionForm.querySelector('[name="buttonLabel"]').value = section.buttonLabel || '';
                    addSectionForm.querySelector('[name="buttonUrl"]').value = section.buttonUrl || '';
                    removeButtonRow.style.display = section.buttonLabel ? 'flex' : 'none';
                }

                // ---- Populate common fields for ALL layouts ----
                addSectionForm.querySelector('[name="position"]').value = section.position || 'bottom';
                showInNav.checked = !!section.showInNav;
                sectionNavCategory.value = section.navCategory || 'about';
                navCatRow.style.display = showInNav.checked ? 'block' : 'none';

                // ---- Final UI updates for editing ----
                submitSectionBtn.textContent = 'Update Section';
                cancelEditBtn.style.display = 'inline-block';
                addSectionForm.scrollIntoView({ behavior: 'smooth' });
            }
        });



       

        /* ░░░ 3. PAGE MANAGEMENT ░░░ */
        const pagesTbody             = document.querySelector('#pagesTable tbody');
        const pageForm               = document.getElementById('pageForm');
        const submitPageBtn          = document.getElementById('submitPageBtn');
        const cancelPageEditBtn      = document.getElementById('cancelPageEditBtn');
        const pageEditOptions        = document.getElementById('pageEditOptions');
        const currentPageImagePreview= document.getElementById('currentPageImagePreview');

        let allPages = [];

        /* helper – clear & restore "create" state */
        function resetPageForm() {
          pageForm.reset();
          delete pageForm.dataset.editId;
          document.getElementById('pageFormHeading').textContent = 'Create a New Page';
          submitPageBtn.textContent = 'Create Page';
          cancelPageEditBtn.style.display = 'none';
          pageEditOptions.style.display   = 'none';
          currentPageImagePreview.style.display = 'none';
          currentPageImagePreview.innerHTML = '';
        }

        /* list pages */
        async function loadPages() {
          const r = await fetch('/api/sections?isFullPage=true');
          if (!r.ok) return alert('Error loading pages: ' + await r.text());
          allPages = await r.json();
          pagesTbody.innerHTML = '';
          allPages.forEach(p => {
            const row = document.createElement('tr');
            row.innerHTML = `
              <td>${p.heading}</td>
              <td><a href="/page/${p.slug}" target="_blank">/page/${p.slug}</a></td>
              <td>${p.isPublished ? '✅ Published' : '⏸️ Draft'}</td>
              <td>
                <button class="edit-page"    data-id="${p._id}">✏️</button>
                <button class="toggle-page"  data-id="${p._id}" data-pub="${p.isPublished}">${p.isPublished?'⏸️':'▶️'}</button>
                <button class="delete-page"  data-id="${p._id}">🗑️</button>
              </td>`;
            pagesTbody.appendChild(row);
          });
        }

        // Load pages on init
        await loadPages();

        /* create OR update */
        pageForm.addEventListener('submit', async e => {
          e.preventDefault();
          const fd    = new FormData(pageForm);
          const editId= pageForm.dataset.editId;

          fd.append('isFullPage','true');

          // Add navigation category for pages
          const pageNavCategory = document.getElementById('pageNavCategory');
          fd.set('navCategory', pageNavCategory.value || 'about');
          fd.set('isPublished', document.getElementById('isPublished').checked?'true':'false');
          fd.set('buttonLabel', fd.get('buttonLabel') || '');
          fd.set('buttonUrl',   fd.get('buttonUrl')   || '');

          /* remove flags */
          if (fd.get('removeButton')) fd.set('removeButton','true');
          if (fd.get('removeImage'))  fd.set('removeImage','true');

          /* upload replacement image */
          const img = document.getElementById('pageImage').files[0];
          if (img) {
            try { fd.set('imagePath', await uploadImage(img,'pages')); }
            catch(err){ return alert('Image upload failed: '+err.message); }
          }
          fd.delete('image');

          if (editId) fd.append('editId', editId);

          const r = await fetch('/api/sections', { method:'POST', body:fd });
          if (!r.ok) return alert(await r.text());

          alert(`Page ${editId?'updated':'created'} successfully!`);
          resetPageForm();
          loadPages();
        });

        /* table actions */
        pagesTbody.addEventListener('click', async e => {
          const btn = e.target.closest('button');
          if (!btn) return;
          const id  = btn.dataset.id;

          /* DELETE */
          if (btn.classList.contains('delete-page')) {
            if (!confirm('Delete this page?')) return;
            const r = await fetch(`/api/sections?id=${id}`, {method:'DELETE'});
            if (!r.ok) return alert(await r.text());
            loadPages(); return;
          }

          /* TOGGLE publish */
          if (btn.classList.contains('toggle-page')) {
            const current = btn.dataset.pub === 'true';
            const r = await fetch('/api/sections', {
              method:'POST',
              body: new URLSearchParams({editId:id,isPublished:(!current).toString()})
            });
            if (!r.ok) return alert(await r.text());
            loadPages(); return;
          }

          /* EDIT */
          if (btn.classList.contains('edit-page')) {
            const p = allPages.find(x=>x._id===id);
            if (!p) return alert('Page not found.');

            /* populate form */
            pageForm.dataset.editId = id;
            pageForm.querySelector('[name="slug"]').value   = p.slug;
            pageForm.querySelector('[name="heading"]').value= p.heading;
            pageForm.querySelector('[name="text"]').value   = p.text;
            pageForm.querySelector('[name="buttonLabel"]').value = p.buttonLabel || '';
            pageForm.querySelector('[name="buttonUrl"]').value   = p.buttonUrl   || '';
            pageForm.querySelector('[name="isPublished"]').checked = p.isPublished;

            if (p.image) {
              currentPageImagePreview.innerHTML =
                `<p style="margin-bottom:5px">Current Image:</p>
                 <img src="${p.image}" style="max-width:120px;border-radius:4px">`;
              currentPageImagePreview.style.display='block';
            }

            document.getElementById('pageFormHeading').textContent = 'Edit Page';
            submitPageBtn.textContent = 'Update Page';
            cancelPageEditBtn.style.display = 'inline-block';
            pageEditOptions.style.display   = 'block';
            pageForm.scrollIntoView({behavior:'smooth'});
          }
        });

        /* cancel */
        cancelPageEditBtn.addEventListener('click', resetPageForm);

        /* initial */
        await loadPages();
        resetPageForm();          // just in case
    </script>
</body>
</html>
