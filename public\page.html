<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tutors Alliance Scotland - Dynamic Page</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/responsive-helper.js" defer></script>
    <script src="/js/rolling-banner.js" defer></script>
    <script src="/js/visual-editor.js" defer></script>
</head>
<body class="dynamic-page" data-page="page">
    <header>
        <h1>Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="/" class="banner-login-link login-box">Home</a>
            <a href="/login.html?role=admin" class="banner-login-link login-box">Login</a>
        </div>
    </header>

    <nav class="main-nav">
        <button class="nav-toggle" aria-expanded="false" aria-controls="primary-menu">
            <span class="sr-only">Toggle navigation</span>
            ☰
        </button>
        <ul id="primary-menu">
            <!-- For Tutors Dropdown -->
            <li class="has-submenu">
                <a href="/tutorMembership.html" aria-haspopup="true">For Tutors</a>
                <ul class="submenu">
                    <li><a href="/tutorMembership.html">Why Join</a></li>
                    <li><a href="/tutorMembership.html#benefits">Membership Benefits</a></li>
                    <li><a href="/tutoring-standards.html">CPD & Training</a></li>
                    <li><a href="/tutoring-standards.html#code-of-conduct">Code of Conduct</a></li>
                    <li><a href="/tutorDirectory.html">Tutor Directory</a></li>
                </ul>
            </li>

            <!-- For Parents Dropdown -->
            <li class="has-submenu">
                <a href="/parents.html" aria-haspopup="true">For Parents</a>
                <ul class="submenu">
                    <li><a href="/parents.html#why-choose">Why Choose a TAS Tutor</a></li>
                    <li><a href="/tutorDirectory.html">Search for a Tutor</a></li>
                    <li><a href="/safeguarding-policy.html">Safeguarding & Complaints</a></li>
                    <li><a href="/faq.html">FAQs</a></li>
                    <li><a href="/parent-events.html">Parent Events / Workshops</a></li>
                </ul>
            </li>

            <!-- About TAS Dropdown (includes dynamic pages) -->
            <li class="has-submenu">
                <a href="/about-us.html" aria-haspopup="true">About TAS</a>
                <ul class="submenu" id="about-tas-submenu">
                    <li><a href="/about-us.html">About Us</a></li>
                    <li><a href="/about-us.html#team">Meet the Team</a></li>
                    <li><a href="/about-us.html#impact">Our Impact</a></li>
                    <li><a href="/blog">News & Media</a></li>
                    <li><a href="/partnerships.html">Partnerships</a></li>
                    <!-- Dynamic pages will be added here by JavaScript -->
                </ul>
            </li>

            <!-- Standalone Contact Link -->
            <li><a href="/contact.html">Contact Us</a></li>
        </ul>
    </nav>

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS or server code can populate tutor names/subjects here -->
        </div>
    </div>

    <main>
        <div class="mission-row">
            <!-- LEFT COLUMN: Shield + Ribbons -->
            <div class="left-col">
                <img src="/images/centralShield.png" alt="Large STA Shield" class="main-shield" id="imageShield">
                <img src="/images/bannerWithRibbons.png" alt="Banner Ribbon" class="main-ribbons" id="imageBanner">
            </div>

            <!-- RIGHT COLUMN: Dynamic page content -->
            <div class="right-col" id="pageContent">
                <!-- Dynamic page content will be inserted here -->
                <div class="loading-indicator">Loading page content now...</div>
            </div>
        </div>

        <!-- Clear separator before dynamic sections -->
        <div class="dynamic-sections-separator"></div>

        <!-- Dynamic sections will be added here -->
        <section id="dynamicSections"></section>
    </main>

    <!-- SOCIAL ICONS FOOTER -->
    <footer class="site-footer fade-in-section">
        <div class="footer-icons">
            <a href="#" aria-label="Pinterest"><i class="fab fa-pinterest-p"></i></a>
            <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
            <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
        </div>
    </footer>

    <!-- STATIC BOTTOM FOOTER -->
    <footer class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4>Extra Information</h4>
                <ul>
                    <li><a href="tutoring-standards.html">Our Tutoring Standards</a></li>
                    <li><a href="faq.html">FAQ's</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="safeguarding-policy.html">Safeguarding Policy</a></li>
                    <li><a href="terms-and-conditions.html">Terms and Conditions</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p>ALL RIGHTS RESERVED © Tutors Alliance Scotland 2023</p>
                    <p>Tutors Alliance Scotland is VAT registered</p>
                    <p>VAT No 429 8003 10</p>
                </div>
                <div class="static-footer-credits">
                    <p>Website by <a href="#" target="_blank">Tutors Alliance Scotland</a></p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="business-insurance">
                    <img src="/images/centralShield.png" alt="Business Insurance" class="insurance-logo">
                    <p>Business Insurance provided through Tutors Alliance Scotland</p>
                    <a href="#" class="insurance-link">View our insurance details</a>
                </div>
                <div class="website-url">
                    <p>www.tutorsalliancescotland.org.uk</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <script>
        function goToLogin(role) {
            window.location.href = `/login.html?role=${encodeURIComponent(role)}`;
        }

        // Rolling banner is now handled by rolling-banner.js

        window.addEventListener('DOMContentLoaded', () => {
            // After the heading's animation delay (say 1.5s) plus a little buffer:
            setTimeout(() => {
                document.querySelectorAll('.fade-later').forEach(el => {
                    el.classList.add('fade-in');
                });
            }, 1500); // or 2000 if you want a bit more buffer
        });
    </script>

    <script>
        // Single Intersection Observer for all fade-in elements
        const fadeObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('is-visible');
                    fadeObserver.unobserve(entry.target);
                }
            });
        }, {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        });

        // Function to observe all fade-in elements
        function observeFadeElements() {
            // Observe all sections with fade-in-section class
            document.querySelectorAll('.fade-in-section').forEach(section => {
                fadeObserver.observe(section);
            });
        }

        // Initial observation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            observeFadeElements();
        });

        // Watch for dynamically added content
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length) {
                    observeFadeElements();
                }
            });
        });

        // Start observing the document body for changes
        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>

    <script type="module">
        // Get the page slug from the URL
        const pathParts = location.pathname.split('/');
        const slug = pathParts[pathParts.length - 1];

        console.log('Loading page with slug:', slug);

        // Fetch the page content
        fetch(`/api/page?slug=${slug}`)
            .then(r => {
                if (!r.ok) {
                    console.error('API response not OK:', r.status, r.statusText);
                    throw new Error('Page not found');
                }
                return r.json();
            })
            .then(page => {
                console.log('Page data received:', page);
                document.title = `${page.heading} - Tutors Alliance Scotland`;

                document.getElementById('pageContent').innerHTML = `
                    <h1>${page.heading}</h1>
                    ${page.image ? `<img src="${page.image}" alt="${page.heading}" class="page-featured-image">` : ''}
                    <div class="page-content">${page.text}</div>
                    ${page.buttonLabel && page.buttonUrl ?
                        `<div style="text-align:center;margin:2rem 0;">
                           <a class="button aurora" href="${page.buttonUrl}">${page.buttonLabel}</a>
                         </div>` : ''}
                `;
            })
            .catch(error => {
                console.error('Error loading page:', error);
                document.getElementById('pageContent').innerHTML = `
                    <h1>Page Not Found</h1>
                    <p>Sorry, the page you requested could not be found.</p>
                    <p><a href="/">Return to Home</a></p>
                `;
            });

        // Load any additional dynamic sections for this page
        fetch(`/api/sections?page=${slug}`)
            .then(r => r.json())
            .then(list => {
                console.log('Dynamic sections for page:', list);
                if (list && list.length > 0) {
                    // Only show the dynamic sections container if there are sections to display
                    const host = document.getElementById('dynamicSections');

                    // Clear any existing content
                    host.innerHTML = '';

                    // Add each section with proper spacing and styling
                    list.forEach((s, index) => {
                        host.insertAdjacentHTML('beforeend', `
                          <article class="dyn-block fade-in-section" style="transition-delay: ${index * 0.1}s">
                            ${s.image ? `<img src="${s.image}" alt="${s.heading}" loading="lazy">` : ''}
                            <h2>${s.heading}</h2>
                            <div class="dyn-content">${s.text}</div>
                          </article>`);
                    });

                    // Make sure the dynamic sections container is visible
                    host.style.display = 'block';

                    // Ensure the separator is visible
                    document.querySelector('.dynamic-sections-separator').style.display = 'block';

                    // Force layout recalculation to ensure proper positioning
                    document.body.offsetHeight;

                    // Observe the newly added dynamic blocks for fade-in animation
                    document.querySelectorAll('.dyn-block').forEach(block => {
                        fadeObserver.observe(block);
                    });
                } else {
                    // If no dynamic sections, hide the container and separator
                    const host = document.getElementById('dynamicSections');
                    host.style.display = 'none';
                    document.querySelector('.dynamic-sections-separator').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading dynamic sections:', error);
                // Hide the container and separator on error
                document.getElementById('dynamicSections').style.display = 'none';
                document.querySelector('.dynamic-sections-separator').style.display = 'none';
            });
    </script>

    <!-- order matters -- all scripts are defered so they execute in DOM order -->

    <script src="/responsive-helper.js" defer></script>   <!-- defines initRollingBanner -->
    <script src="/js/rolling-banner.js" defer></script>   <!-- *uses* initRollingBanner -->
    <script src="/js/dynamic-nav.js" defer></script>
    <script src="/js/visual-editor.js" defer></script>

    <!-- bump the version whenever you redeploy -->
    <script src="/js/dynamic-sections.js?v=20240530" type="module" defer></script>
</body>
</html>
