<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tutors Alliance Scotland - Parent Zone</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/responsive-helper.js"></script>
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>
    <style>
        /* Hide legoLeft and legoRight images in portrait orientation on restricted viewports */
        @media (orientation: portrait) {
            .tutor-img-list-left, .tutor-img-list-right {
                display: none !important;
            }
        }
    </style>
</head>
<body data-page="parents">
    <!-- Shared banner/header -->
    <header>
        <h1>Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="/" class="banner-login-link login-box">Home</a>
            <a href="login.html?role=admin" class="banner-login-link login-box">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS will populate tutor names/subjects here -->
        </div>
    </div>

    <main>
        <div class="mission-row fade-in-section">
            <!-- LEFT COLUMN: Shield + Ribbons -->
            <div class="left-col">
                <img src="/images/centralShield.png" alt="Large STA Shield" class="main-shield" id="imageShield">
                <img src="/images/bannerWithRibbons.png" alt="Banner Ribbon" class="main-ribbons" id="imageBanner">
            </div>

            <!-- RIGHT COLUMN: heading + about-us text -->
            <div class="right-col">
                <div class="about-us-landing" id="aboutUsLanding">
                    <h1 class="mission-statement">Parent Zone</h1>
                </div>
            </div>
        </div>

        <!-- TOP dynamic sections container -->
        <section id="dynamicSectionsTop" class="dynamic-section-container fade-in-section"></section>

        <!-- PARENT INTRO: Two-column content -->
        <section id="why-choose" class="two-col-content fade-in-section">
            <div>
                <h2>Find trusted, professional tutors who put your child first.</h2>
                <p>
                    At Tutors Alliance Scotland, we believe every child deserves high-quality educational support from a tutor who is skilled, safe and supported. That's why we created a national professional membership for tutors who want to do things properly for your peace of mind.
                </p>
                <a href="tutorDirectory.html" class="button aurora">Find a Tutor</a>
            </div>
            <div>
                <div class="form-container" style="background-color: #fff; border-radius: 1rem; padding: 2rem; box-shadow: 0 2px 18px rgba(0,87,183,0.05);">
                    <h2 style="color: #001B44; margin-bottom: 1.5rem; text-align: center;">Tutor Finder</h2>
                    <form id="tutorFinderForm" action="/tutors" method="GET" style="display: flex; flex-direction: column; gap: 1.5rem;">
                        <div>
                            <label for="subject" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333;">Subject</label>
                            <select name="subject" id="subject" style="width: 100%; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                                <option value="mathematics">Mathematics</option>
                                <option value="english">English</option>
                            </select>
                        </div>

                        <div>
                            <label for="mode" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333;">Location</label>
                            <select name="mode" id="mode" style="width: 100%; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                                <option value="online">Online</option>
                                <option value="in-person">In Person</option>
                            </select>
                        </div>

                        <div id="postcodeContainer" style="display: none;">
                            <label for="postcode" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333;">Postcode</label>
                            <input type="text" name="postcode" id="postcode" placeholder="Enter your postcode" style="width: 100%; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                        </div>

                        <button type="submit" class="button aurora" style="background-color: #0057B7; color: white; padding: 1rem; border: none; border-radius: 0.5rem; font-size: 1.1rem; font-weight: bold; cursor: pointer; margin-top: 0.5rem;">Find Tutors</button>
                    </form>
                </div>
            </div>
        </section>

        <!-- MIDDLE dynamic sections container -->
        <section id="dynamicSectionsMiddle" class="dynamic-section-container"></section>

        <!-- WHAT MAKES OUR TUTORS DIFFERENT: Full-width section -->
        <section class="tutor-zone-section fade-in-section">
            <div class="zone-gradient-bg tutor-gradient-bg">
                <div class="zone-list-row">
                    <div class="tutor-box curve-bottom-left">
                        <h2>⭐ What Makes Our Tutors Different?</h2>
                        <p>All TAS members:</p>
                        <ul style="text-align:left; max-width: 600px; margin: 1rem auto; list-style-type: none; padding-left: 0;">
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">✅ Follow a Code of Professional Conduct</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">🛡️ Hold up-to-date PVG certificates</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">📚 Engage in ongoing training and CPD</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">🧠 Are fully qualified teachers who understand how children learn and how to close learning gaps</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">🧑‍🏫 Are part of a professional community that encourages reflection, sharing and accountability</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">🗣️ Care deeply about your child's wellbeing as well as their academic progress</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- WHAT TO LOOK FOR: Two-column content -->
        <section class="two-col-content fade-in-section">
            <div>
                <h2>👀 What Should I Look for in a Tutor?</h2>
                <p>We know it can feel overwhelming to choose the right tutor. That's why we help parents make informed choices by sharing what good tutoring looks like.</p>
                <p>We recommend you choose a tutor who:</p>
                <ul>
                    <li>Is clear about their qualifications and experience</li>
                    <li>Has safeguarding training and a PVG</li>
                    <li>Can explain how they will help your child, not just what they'll cover</li>
                    <li>Works in partnership with you, providing regular feedback and support</li>
                    <li>Is a member of a professional body like TAS</li>
                </ul>
                <a href="tutorDirectory.html" class="button aurora">Browse Our Tutors</a>
            </div>
            <div>
                <img src="/images/parent1.PNG" alt="Professional tutor" />
            </div>
        </section>

        <!-- WORKING WITH A TUTOR: Full-width section -->
        <section class="parents-zone-section fade-in-section">
            <div class="zone-gradient-bg parent-gradient-bg" style="background: linear-gradient(135deg, #0057B7 0%, #003A7A 100%);">
                <div class="zone-list-row">
                    <div class="parent-box curve-bottom-left">
                        <h2 style="color: #FFFFFF; text-shadow: 0 1px 3px rgba(0,0,0,0.2);">🤝 Working with a Tutor: What to Expect</h2>
                        <ul class="parent-list" style="text-align:center; max-width: 600px; margin: 1.5rem auto; list-style-type: none; padding-left: 0;">
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">A clear and structured start with a baseline assessment or discussion about goals</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">Consistent, personalised lessons</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">Communication between sessions if needed</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">Respect for your child's needs, pace and confidence</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">A professional approach to scheduling, holidays and payment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- HOW WE HELP PARENTS: Two-column content -->
        <section class="two-col-content fade-in-section">
            <div>
                <h2>🔍 How We Help Parents</h2>
                <ul>
                    <li>Provide a directory of verified tutors (coming soon)</li>
                    <li>Support partnership organisations to connect families with funded tuition</li>
                    <li>Share resources and guides to help you support learning at home</li>
                    <li>Advocate for fair access to tuition for all children in Scotland</li>
                </ul>
            </div>
            <div>
                <img src="/images/parent2.PNG" alt="How we help parents" />
            </div>
        </section>

        <!-- THE BIGGER PICTURE: Full-width section -->
        <section class="tutor-zone-section fade-in-section">
            <div class="zone-gradient-bg tutor-gradient-bg">
                <div class="zone-list-row">
                    <img src="/images/legoLeft.png" alt="Lego Left" class="tutor-img-list tutor-img-list-left" style="position: absolute; left: -97px; top: -27px;">
                    <div class="tutor-box curve-bottom-left">
                        <h2>🧒 The Bigger Picture</h2>
                        <p>We are working across Scotland to ensure that tutoring becomes part of the wider educational landscape, not just a luxury. Through our partnerships, we support charities and local organisations to bring high-quality tuition to more children, including those facing disadvantage.</p>
                        <a href="partnerships.html" class="button aurora">Learn About Our Partnerships</a>
                    </div>
                    <img src="/images/legoRight.png" alt="Lego Right" class="tutor-img-list tutor-img-list-right" style="top: -77px; position: absolute; right:-77px;">
                </div>
            </div>
        </section>

        <!-- FAQ SECTION -->
        <section class="faq-section fade-in-section">
            <div class="faq-overlay-card curve-bottom-right">
                <h2>❓ Frequently Asked Questions</h2>
                <div class="faq-container">
                    <div class="faq-item">
                        <h3>1. What ages and subjects do TAS tutors support?</h3>
                        <p>TAS members support children and young people from early years to SQA exam levels and even beyond. We have tutors covering a wide range of subjects including English, maths, sciences, languages and more.</p>
                    </div>
                    <div class="faq-item">
                        <h3>2. Are TAS tutors background checked?</h3>
                        <p>Yes. All members must have an up-to-date PVG (Protecting Vulnerable Groups) certificate or equivalent. We take safeguarding seriously and expect all tutors to have completed safeguarding training and to renew it regularly.</p>
                    </div>
                    <div class="faq-item">
                        <h3>3. How do I know if a tutor is a TAS member?</h3>
                        <p>TAS members can display our digital membership badge and are listed on our upcoming online directory. You can also ask to see their membership confirmation or contact us to verify.</p>
                    </div>
                    <div class="faq-item">
                        <h3>4. Can my child work with a TAS tutor online?</h3>
                        <p>Absolutely. Many of our tutors offer high-quality online tutoring using safe, secure platforms. They are trained in keeping children safe online and using digital tools to make learning engaging and effective.</p>
                    </div>
                    <div class="faq-item">
                        <h3>5. How can I access funded tutoring support?</h3>
                        <p>Tutors Alliance Scotland works with charities and other organisations across Scotland to support children through funded programmes. If your child attends a school or organisation working in partnership with us, they may be eligible. Ask your school or local education service about any tuition opportunities available and if they work with TAS.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- FIND A TUTOR: Full-width section -->
        <section class="parents-zone-section fade-in-section">
            <div class="zone-gradient-bg parent-gradient-bg" style="background: linear-gradient(135deg, #0057B7 0%, #003A7A 100%);">
                <div class="zone-list-row">
                    <div class="parent-box curve-bottom-left">
                        <h2 style="color: #FFFFFF; text-shadow: 0 1px 3px rgba(0,0,0,0.2);">🔍 Find a TAS-Approved Tutor</h2>
                        <p style="color: #F0F8FF; font-weight: 500; margin-bottom: 1.5rem;">Looking for a tutor you can trust?</p>
                        <p style="color: #F0F8FF; font-weight: 500; margin-bottom: 1.5rem;">Our TAS Tutors Directory is your starting point for finding experienced, professional and vetted tutors across Scotland. Whether you're looking for subject-specific support, exam preparation or a boost in confidence, we've got you covered.</p>
                        <p style="color: #F0F8FF; font-weight: 500; margin-bottom: 1.5rem;">Every tutor listed in our directory:</p>
                        <ul class="parent-list" style="text-align:center; max-width: 600px; margin: 1.5rem auto; list-style-type: none; padding-left: 0;">
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">Is a TAS member who meets our professional standards</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">Holds an up-to-date PVG</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">Is committed to safeguarding and ongoing development</li>
                            <li style="margin-bottom: 0.8rem; padding: 0.8rem; background-color: rgba(255,255,255,0.8); border-radius: 0.5rem; font-weight: 500;">Shares our values of equity, empathy and excellence</li>
                        </ul>
                        <p style="color: #F0F8FF; font-weight: 500;">You can contact tutors directly to find out more about availability, pricing and areas of expertise.</p>
                        <a href="tutorDirectory.html" class="button aurora">Browse the TAS Tutors Directory</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA BUTTONS WITH BACKGROUND -->
        <section class="cta-banner fade-in-section">
            <div class="cta-content">
                <div class="cta-buttons">
                    <a class="button aurora" href="tutorDirectory.html">Find a Tutor</a>
                    <a class="button aurora" href="contact.html">Contact Us</a>
                    <a class="button aurora" href="partnerships.html">Learn About Partnerships</a>
                </div>
            </div>
        </section>

        <!-- Clear separator before dynamic sections -->
        <div class="dynamic-sections-separator fade-in-section"></div>

        <!-- BOTTOM dynamic sections container -->
        <section id="dynamicSections" class="dynamic-section-container fade-in-section"></section>

        <div style="clear: both; width: 100%; height: 50px;" class="fade-in-section"></div>
    </main>

    <!-- SOCIAL ICONS FOOTER -->
    <footer class="site-footer fade-in-section">
        <div class="footer-icons">
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
        </div>
    </footer>

    <!-- STATIC BOTTOM FOOTER -->
    <footer class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4>Extra Information</h4>
                <ul>
                    <li><a href="tutoring-standards.html">The TAS Way: Governance and Guidance</a></li>
                    <li><a href="faq.html">FAQ's</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="safeguarding-policy.html">Safeguarding Policy</a></li>
                    <li><a href="terms-and-conditions.html">Terms and Conditions</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p>ALL RIGHTS RESERVED © Tutors Alliance Scotland 2025</p>
                </div>
                <div class="static-footer-credits">
                    <p>Website by <a href="#" target="_blank">Tutors Alliance Scotland</a></p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="website-url">
                    <p>www.tutorsalliancescotland.org.uk</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <script>
        // Show/hide postcode container
        document.getElementById('mode').addEventListener('change', function () {
            const postcodeContainer = document.getElementById('postcodeContainer');
            if (this.value === 'in-person') {
                postcodeContainer.style.display = 'block';
            }
            else {
                postcodeContainer.style.display = 'none';
            }
        });

        // Handle form submission
        document.getElementById('tutorFinderForm').addEventListener('submit', function (event) {
            event.preventDefault();
            const subject = document.getElementById('subject').value;
            const mode = document.getElementById('mode').value;
            let postcode = document.getElementById('postcode').value;

            if (postcode) {
                postcode = postcode.toUpperCase();
            }

            let queryParams = `?subject=${encodeURIComponent(subject)}&mode=${encodeURIComponent(mode)}`;
            if (mode === "in-person" && postcode.trim() !== "") {
                queryParams += `&postcode=${encodeURIComponent(postcode)}`;
            }
            window.location.href = `/tutors/search${queryParams}`;
        });

        // All fade-in logic is now handled centrally by responsive-helper.js

    </script>

    <!-- order matters -- all scripts are defered so they execute in DOM order -->

    <script src="/js/rolling-banner.js" defer></script>   <!-- *uses* initRollingBanner -->
    <script src="/js/visual-editor.js" defer></script>

    <!-- bump the version whenever you redeploy -->
    <script src="/js/dynamic-sections.js?v=20240530" type="module" defer></script>
</body>
</html>
