<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Blog Writer – Tutors Alliance Scotland</title>

    <!-- assets -->
    <link rel="icon" href="/images/bannerShield2.png" type="image/png" />
    <link rel="stylesheet" href="/styles2.css" />
    <link rel="stylesheet" href="/header-banner.css" />
    <link rel="stylesheet" href="/css/nav.css" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <script src="/responsive-helper.js" defer></script>
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>

    <!-- SEO metadata -->
    <meta name="description" content="Tutors Alliance Scotland blog management system - create and manage educational blog posts for tutors and parents in Scotland." />
    <meta name="keywords" content="tutoring, Scotland, education, blog, tutors alliance" />
    <meta name="author" content="Tutors Alliance Scotland" />
    
    <!-- Open Graph metadata for social sharing -->
    <meta property="og:title" content="Blog Writer - Tutors Alliance Scotland" />
    <meta property="og:description" content="Create and manage educational blog posts for tutors and parents in Scotland." />
    <meta property="og:image" content="https://tutorsalliancescotland.co.uk/images/bannerShield2.png" />
    <meta property="og:url" content="https://tutorsalliancescotland.co.uk/blogWriter.html" />
    <meta property="og:type" content="website" />

    <!-- JSON‑LD placeholder (filled on submit) -->
    <script id="blogMicrodata" type="application/ld+json"></script>

    <!-- blog‑writer guard -->
    <script>
        (async () => {
            try {
                const ok = await fetch('/api/protected?role=blogwriter', {
                    credentials: 'include' // Important: include cookies for authentication
                });
                if (!ok.ok) {
                    console.error('Authentication failed:', await ok.text());
                    throw new Error('not writer');
                }
            } catch (error) {
                console.error('Authentication error:', error);
                location.href = '/login.html?role=blogwriter';
            }
        })();
    </script>

    <style>
        .wide-input {
            width: 100%; /* full width of the lilac panel                */
            padding: 12px 14px; /* taller & roomier                              */
            font-size: 1.15rem; /* larger text – tweak to taste (1rem ≈ 16 px)   */
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box; /* keeps the width from overflowing          */
        }
        
        /* Tab styling */
        .blog-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #0057B7;
        }
        
        .tab-btn {
            padding: 10px 20px;
            background-color: #f0f0f0;
            border: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
            margin-right: 5px;
            font-weight: bold;
        }
        
        .tab-btn.active {
            background-color: #0057B7;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Blog list styling */
        .blog-list-container {
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        #blogListTable {
            width: 100%;
            border-collapse: collapse;
        }
        
        #blogListTable th, #blogListTable td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        #blogListTable th {
            background-color: #f2f2f2;
            position: sticky;
            top: 0;
        }
        
        .blog-filter {
            margin-bottom: 20px;
        }
        
        .loading-message {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .delete-btn {
            background-color: #ff4d4d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .delete-btn:hover {
            background-color: #ff0000;
        }
    </style>
</head>
<body>
    <!-- HEADER / NAV identical to other pages -->
    <header>
        <h1>Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a class="banner-login-link login-box" href="/">Home</a>
            <a class="banner-login-link login-box" href="/login.html?role=admin">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner"></div>
    </div>

    <!-- ── BLOG MANAGEMENT INTERFACE ───────────────────────── -->
    <main>
        <!-- Tab navigation -->
        <div class="blog-tabs">
            <button id="createTabBtn" class="tab-btn active">Create Blog</button>
            <button id="manageTabBtn" class="tab-btn">Manage Blogs</button>
        </div>

        <!-- Create Blog Section -->
        <section id="newBlogSection" class="tab-content active">
            <h2>Create a New Blog Post</h2>

            <form id="blogForm">
                <label>Title:<input id="titleField" class="wide-input" name="title" required /></label>
                <label>Author:<input id="authorField" class="wide-input" name="author" required /></label>

                <label>
                    Category:
                    <select id="categoryField" name="category">
                        <option value="general">General</option>
                        <option value="parent">Parent</option>
                        <option value="tutor">Tutor</option>
                    </select>
                </label>

                <label>
                    Excerpt (short summary):
                    <textarea id="excerptField" rows="2" maxlength="200"></textarea>
                </label>

                <label>
                    Publish Date/Time:
                    <input type="datetime-local" id="publishDateField" name="publishDate" />
                </label>

                <label>
                    Content:
                    <textarea id="contentField" rows="6" required></textarea>
                </label>

                <label>
                    Image:
                    <input type="file" id="imageField" accept="image/*" />
                </label>

                <button type="submit">Create Blog</button>
            </form>
        </section>

        <!-- Manage Blogs Section -->
        <section id="manageBlogSection" class="tab-content">
            <h2>Manage Existing Blog Posts</h2>
            
            <div class="blog-filter">
                <label for="blogCategoryFilter">Filter by category:</label>
                <select id="blogCategoryFilter">
                    <option value="all">All Categories</option>
                    <option value="general">General</option>
                    <option value="parent">Parent</option>
                    <option value="tutor">Tutor</option>
                </select>
                <button id="refreshBlogsBtn">Refresh List</button>
            </div>
            
            <div class="blog-list-container">
                <table id="blogListTable">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Author</th>
                            <th>Category</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="blogListBody">
                        <!-- Blog entries will be loaded here -->
                        <tr>
                            <td colspan="5" class="loading-message">Loading blog posts...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <!-- JS as module so we can import upload‑helper -->
    <script type="module">
        import { uploadImage } from '/js/upload-helper.js';

        /* rolling banner */
        fetch('/api/tutors?format=json')
            .then(r => r.json())
            .then(list => {
                tutorBanner.textContent =
                    list.map(t => `${t.name} (${t.subjects.join(', ')})`).join(' | ');
            })
            .catch(console.error);

        /* Tab switching functionality */
        const createTabBtn = document.getElementById('createTabBtn');
        const manageTabBtn = document.getElementById('manageTabBtn');
        const newBlogSection = document.getElementById('newBlogSection');
        const manageBlogSection = document.getElementById('manageBlogSection');
        
        createTabBtn.addEventListener('click', () => {
            createTabBtn.classList.add('active');
            manageTabBtn.classList.remove('active');
            newBlogSection.classList.add('active');
            manageBlogSection.classList.remove('active');
        });
        
        manageTabBtn.addEventListener('click', () => {
            manageTabBtn.classList.add('active');
            createTabBtn.classList.remove('active');
            manageBlogSection.classList.add('active');
            newBlogSection.classList.remove('active');
            loadBlogs(); // Load blogs when tab is clicked
        });
        
        /* Load blogs function */
        async function loadBlogs(category = 'all') {
            const blogListBody = document.getElementById('blogListBody');
            blogListBody.innerHTML = '<tr><td colspan="5" class="loading-message">Loading blog posts...</td></tr>';
            
            try {
                const response = await fetch('/api/blog-writer', {
                    credentials: 'include' // Include cookies for authentication
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to fetch blogs: ${response.status} ${response.statusText}`);
                }
                
                const blogs = await response.json();
                
                // Filter blogs by category if needed
                let filteredBlogs = blogs;
                if (category !== 'all') {
                    if (category === 'general') {
                        // General means both parent and tutor categories
                        filteredBlogs = blogs.filter(blog => 
                            blog.category.includes('parent') && blog.category.includes('tutor')
                        );
                    } else {
                        // Filter by specific category
                        filteredBlogs = blogs.filter(blog => blog.category.includes(category));
                    }
                }
                
                // Clear loading message
                blogListBody.innerHTML = '';
                
                if (filteredBlogs.length === 0) {
                    blogListBody.innerHTML = '<tr><td colspan="5" class="loading-message">No blog posts found</td></tr>';
                    return;
                }
                
                // Add each blog to the table
                filteredBlogs.forEach(blog => {
                    const row = document.createElement('tr');
                    
                    // Format date
                    const date = new Date(blog.publishDate || blog.createdAt);
                    const formattedDate = date.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                    });
                    
                    // Format category
                    let categoryText = blog.category.join(', ');
                    if (blog.category.includes('parent') && blog.category.includes('tutor')) {
                        categoryText = 'General';
                    }
                    
                    row.innerHTML = `
                        <td>${blog.title}</td>
                        <td>${blog.author}</td>
                        <td>${categoryText}</td>
                        <td>${formattedDate}</td>
                        <td>
                            <button class="delete-btn" data-id="${blog._id}">Delete</button>
                        </td>
                    `;
                    
                    blogListBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error loading blogs:', error);
                blogListBody.innerHTML = `<tr><td colspan="5" class="loading-message">Error: ${error.message}</td></tr>`;
            }
        }
        
        /* Delete blog function */
        document.getElementById('blogListBody').addEventListener('click', async (e) => {
            if (e.target.classList.contains('delete-btn')) {
                const blogId = e.target.dataset.id;
                
                if (confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
                    try {
                        const response = await fetch(`/api/blog-writer?id=${blogId}`, {
                            method: 'DELETE',
                            credentials: 'include' // Include cookies for authentication
                        });
                        
                        if (!response.ok) {
                            throw new Error(`Failed to delete blog: ${response.status} ${response.statusText}`);
                        }
                        
                        alert('Blog post deleted successfully');
                        loadBlogs(document.getElementById('blogCategoryFilter').value); // Reload the list
                    } catch (error) {
                        console.error('Error deleting blog:', error);
                        alert(`Error deleting blog: ${error.message}`);
                    }
                }
            }
        });
        
        /* Filter blogs by category */
        document.getElementById('blogCategoryFilter').addEventListener('change', (e) => {
            loadBlogs(e.target.value);
        });
        
        /* Refresh blogs button */
        document.getElementById('refreshBlogsBtn').addEventListener('click', () => {
            loadBlogs(document.getElementById('blogCategoryFilter').value);
        });

        /* form workflow for creating blogs */
        blogForm.addEventListener('submit', async e => {
            e.preventDefault();

            const title = titleField.value.trim();
            const author = authorField.value.trim();
            const category = categoryField.value;
            const excerpt = excerptField.value.trim();
            const publishDate = publishDateField.value;
            const content = contentField.value.trim();

            /* image first */
            let imagePath = '';
            if (imageField.files[0]) {
                try { imagePath = await uploadImage(imageField.files[0], 'blog'); }
                catch (err) { return alert('Upload failed: ' + err.message); }
            }

            /* JSON‑LD for SEO */
            const nowISO = new Date().toISOString();
            blogMicrodata.textContent = JSON.stringify({
                "@context": "https://schema.org",
                "@type": "BlogPosting",
                "mainEntityOfPage": { "@id": "https://tutorsalliancescotland.co.uk/blog", "@type": "WebPage" },
                headline: title,
                author: { "@type": "Person", name: author || "Tutors Alliance Scotland" },
                publisher: {
                    "@type": "Organization",
                    name: "Tutors Alliance Scotland",
                    logo: { "@type": "ImageObject", url: "https://tutorsalliancescotland.co.uk/images/bannerShield2.png" }
                },
                datePublished: publishDate ? new Date(publishDate).toISOString() : nowISO,
                dateModified: nowISO,
                description: excerpt || (content.slice(0, 160) + '…'),
                image: imagePath || "https://tutorsalliancescotland.co.uk/images/defaultBlog.png"
            }, null, 2);

            /* send to server */
            const payload = { title, author, category, excerpt, publishDate, content, imagePath };
            const r = await fetch('/api/blog-writer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload),
                credentials: 'include' // Important: include cookies for authentication
            });

            if (!r.ok) return alert('Server: ' + await r.text());
            alert('Blog created!');
            blogForm.reset();
            blogMicrodata.textContent = '';
        });
    </script>
</body>
</html>
