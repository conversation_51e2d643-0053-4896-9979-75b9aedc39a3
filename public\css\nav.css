/* ===================================================================== */
/* MAIN NAVIGATION STYLES - Dropdown & Mobile Responsive                */
/* ===================================================================== */

/* === GENERAL LAYOUT ================================================== */
.main-nav {
    background: #001B44;
    position: relative;
    z-index: 1000;
    width: 100%;
}

.main-nav .menu {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav .menu-item {
    position: relative;
    border-left: 1px solid #ADD8E6;
}

.main-nav .menu-item:first-child {
    border-left: none;
}

.main-nav a {
    display: block;
    padding: 14px 20px;
    color: #fff;
    text-decoration: none;
    font-size: 1.1em;
    white-space: nowrap;
    transition: background-color 0.3s ease;
}

.main-nav a:hover,
.main-nav .menu-item:hover > a,
.main-nav .menu-item:focus-within > a {
    background: #003F8F;
}

/* === DROPDOWN SUBMENUS =============================================== */
.has-submenu {
    position: relative;
}

/* Dropdown arrow indicator */
.has-submenu > a::after {
    content: ' ▾';
    font-size: 0.8em;
    margin-left: 0.5em;
}

/* 🔑 CRITICAL: Hide ALL nested ul elements by default */
.main-nav ul ul {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: #003F8F;
    min-width: 240px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 0 0 8px 8px;
    border-top: 3px solid #00a9ce;
    z-index: 1001;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-direction: column;
}

/* 🔑 CRITICAL: Show submenus on hover/focus (desktop) or when .open class is applied (mobile) */
.has-submenu:hover > ul,
.has-submenu:focus-within > ul,
.has-submenu.open > ul {
    display: flex;
}

.main-nav ul ul li {
    border-left: none;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    width: 100%;
}

.main-nav ul ul li:last-child {
    border-bottom: none;
}

.main-nav ul ul a {
    padding: 12px 18px;
    font-size: 0.95em;
    border-left: none;
    transition: background-color 0.2s ease;
}

.main-nav ul ul a:hover {
    background: #0057B7;
}

/* === MOBILE HAMBURGER MENU ========================================== */
.nav-toggle {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 1.8rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 15px;
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* === MOBILE RESPONSIVE STYLES ======================================= */
@media (max-width: 900px) {
    /* Show hamburger button */
    .nav-toggle {
        display: block;
    }

    /* Hide menu by default on mobile */
    .main-nav .menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: #001B44;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* Show menu when nav-open class is applied */
    .main-nav.nav-open .menu {
        display: flex;
    }

    /* Mobile menu item styling */
    .main-nav .menu-item {
        border-left: none;
        border-bottom: 1px solid #ADD8E6;
        width: 100%;
    }

    .main-nav .menu-item:last-child {
        border-bottom: none;
    }

    .main-nav a {
        padding: 15px 20px;
        text-align: left;
        width: 100%;
    }

    /* Mobile accordion indicators */
    .has-submenu > a::after {
        content: ' ▸';
        float: right;
        transition: transform 0.2s;
    }

    .has-submenu.open > a::after {
        transform: rotate(90deg);
    }

    /* Mobile submenus become in-flow lists */
    .main-nav ul ul {
        position: static;
        background: rgba(0,0,0,0.25);
        border-radius: 0;
        box-shadow: none;
        border-top: none;
        min-width: auto;
    }

    .main-nav ul ul a {
        padding-left: 40px;
        font-size: 0.9em;
    }

    /* Hide desktop-only items on mobile */
    .desktop-only {
        display: none;
    }
}

/* === LEGACY SUPPORT ================================================== */
/* Support for existing custom pages dropdown */
.custom-pages-dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #003F8F;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    top: 100%;
    left: 0;
    border-radius: 0 0 4px 4px;
}

.dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.dropdown-content a:hover {
    background-color: #0057B7;
}

.custom-pages-dropdown:hover .dropdown-content {
    display: block;
}
