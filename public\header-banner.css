/* header-banner.css */
/* This file contains the header and rolling banner styles from styles2.css */

/* Header styles */
header {
    /* Two layered backgrounds: shield on top, tartan behind */
    background-image: url('/images/bannerShield2.png'), url('/images/bannerBackground.PNG');
    background-repeat: no-repeat, repeat;
    /* Position the shield 20px from the left & top,
       while the tartan repeats from the top-left. */
    background-position: 20px 20px, left top;
    /* Scale the shield to 100px wide,
       the tartan tiles to 50px wide. */
    background-size: 100px auto, 150px auto;
    /* Banner padding (height) */
    padding: 40px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

/* Move and resize the heading */
header h1 {
    /* Slightly bigger heading */
    font-size: 3.3em;
    font-weight: bold;
    /* Nudge it so it starts after the shield area */
    margin-left: 100px;
    line-height: 1.2;
    text-shadow: 5px 5px 10px #0057B7;
}

/* Lilac box for the login link in the banner */
.login-box {
    background-color: #0057B7; /* blue */
    color: #fff;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
}

.login-box:hover {
    background-color: #b37cb3; /* a darker lilac on hover */
}

/* This container holds both buttons side by side */
.header-links {
    display: flex;
    /* A small gap for the space between them (adjust as needed) */
    gap: 10px;
    /* Right-align the buttons */
    margin-left: auto;
}

/* Navigation styles with dropdown support */
.main-nav {
    background-color: #001B44; /* a deep navy, or #003F8F, etc. */
    position: relative;
    z-index: 1000;
}

.main-nav ul {
    display: flex;
    justify-content: center;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav ul li {
    position: relative;
    border-left: 1px solid #ADD8E6;
}

.main-nav ul li:first-child {
    border-left: none;
}

.main-nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 14px 20px;
    display: block;
    font-size: 1.1em;
    white-space: nowrap;
    transition: background-color 0.3s ease;
}

.main-nav ul li a:hover,
.main-nav ul li:hover > a {
    background-color: #003F8F; /* highlight on hover */
}

/* Dropdown arrow indicator */
.has-submenu > a::after {
    content: ' ▾';
    font-size: 0.8em;
    margin-left: 0.5em;
}

/* Dropdown submenu styling */
.submenu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #003F8F;
    min-width: 240px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 0 0 8px 8px;
    border-top: 3px solid #00a9ce;
    z-index: 1001;
    list-style: none;
    padding: 0;
    margin: 0;
}

.has-submenu:hover .submenu,
.has-submenu:focus-within .submenu {
    display: block;
}

.submenu li {
    border-left: none;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.submenu li:last-child {
    border-bottom: none;
}

.submenu a {
    padding: 12px 18px !important;
    font-size: 0.95em !important;
    border-left: none !important;
    transition: background-color 0.2s ease;
}

.submenu a:hover {
    background-color: #0057B7 !important;
}

/* Mobile hamburger menu */
.nav-toggle {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5em;
    padding: 10px 15px;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 15px;
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Custom pages dropdown styles */
.custom-pages-dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #001B44;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    top: 100%;
    left: 0;
}

.dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    border-bottom: 1px solid #003F8F;
}

.dropdown-content a:hover {
    background-color: #003F8F;
}

.custom-pages-dropdown:hover .dropdown-content {
    display: block;
}

/* Rolling banner styles */
.rolling-banner {
    position: relative;
    width: 100%;
    overflow: hidden;
    border: 3px solid #C8A2C8; /* Lilac outline */
    background-color: #f9f9f9; /* If you want a background */
    height: 2.5em; /* Enough height for the text */
    display: flex;
    align-items: center; /* vertically center the text */
    margin-bottom: 10px;
}

.rolling-content {
    white-space: nowrap;
    display: inline-block;
    padding-left: 100%;
    animation: scrollBanner 15s linear infinite;
    font-weight: bold;
    color: #0057B7;
}

@keyframes scrollBanner {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-100%);
    }
}

/* Mobile responsive navigation */
@media (max-width: 900px) {
    .nav-toggle {
        display: block;
    }

    .main-nav ul {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #001B44;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .main-nav.nav-open ul {
        display: flex;
    }

    .main-nav ul li {
        border-left: none;
        border-bottom: 1px solid #ADD8E6;
        width: 100%;
    }

    .main-nav ul li:last-child {
        border-bottom: none;
    }

    .main-nav ul li a {
        padding: 15px 20px;
        text-align: left;
    }

    /* Mobile accordion for submenus */
    .has-submenu > a::after {
        content: ' ▸';
        float: right;
        transition: transform 0.2s;
    }

    .has-submenu.open > a::after {
        transform: rotate(90deg);
    }

    .submenu {
        position: static;
        display: none;
        background: rgba(0,0,0,0.2);
        border-radius: 0;
        box-shadow: none;
        border-top: none;
        min-width: auto;
    }

    .has-submenu.open .submenu {
        display: block;
    }

    .submenu a {
        padding-left: 40px !important;
        font-size: 0.9em !important;
    }
}
