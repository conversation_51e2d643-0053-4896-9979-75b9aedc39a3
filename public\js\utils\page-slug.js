/**
 * Canonical page slug utility
 * Normalizes URLs to prevent duplicate database entries
 */

export function getPageSlug(path = location.pathname) {
    // Remove trailing slash
    let p = path.replace(/\/$/, '');
    
    // Handle root path
    if (p === '' || p === '/') {
        return 'index';
    }
    
    // Remove leading slash and file extension
    p = p.replace(/^\//, '').replace(/\.html$/, '');
    
    // Convert to lowercase for consistency
    return p.toLowerCase();
}
